<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Interfaces;

use LoginAutonom\ExternalResourcesBundle\DTO\ApiConfigurationDTO;
use Symfony\Component\DependencyInjection\Attribute\AutoconfigureTag;

#[AutoconfigureTag(RequestHandlerInterface::TAG)]
interface RequestHandlerInterface
{
    public const TAG = 'request-handler';

    public function handle(ApiConfigurationDTO $apiConfigurationDTO, array $params = []): mixed;

    public static function type(): string;
}
