<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\DTO;

final class SharedDynamicParamsDTO
{
    private array $dynamicParams = [];

    public function getDynamicParams(): array
    {
        return $this->dynamicParams;
    }

    public function getDynamicParamsByRequestType(string $requestType): array
    {
        return $this->dynamicParams[$requestType] ?? [];
    }

    public function addDynamicParamsByType(string $requestType, array $params): void
    {
        $this->dynamicParams[$requestType] = $params;
    }

    public function hasDynamicParamsByRequestType(string $requestType): bool
    {
        return isset($this->dynamicParams[$requestType]) && !empty($this->dynamicParams[$requestType]);
    }

    public function isEmpty(): bool
    {
        return empty($this->dynamicParams);
    }
}
