<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\DTO;

final readonly class ProcessingContextDTO
{
    public function __construct(
        private string $requestType,
        private array $requestConfig,
        private array $options,
        private ApiConfigurationDTO $apiConfiguration,
    ) {
    }

    public function getRequestType(): string
    {
        return $this->requestType;
    }

    public function getRequestConfig(): array
    {
        return $this->requestConfig;
    }

    public function getOptions(): array
    {
        return $this->options;
    }

    public function getApiConfiguration(): ApiConfigurationDTO
    {
        return $this->apiConfiguration;
    }

    public function hasRequestConfig(): bool
    {
        return !empty($this->requestConfig);
    }

    public function hasOptions(): bool
    {
        return !empty($this->options);
    }
}
