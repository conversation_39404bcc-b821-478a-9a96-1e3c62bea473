<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\DTO;

final class RequestParameterContextDTO
{
    public function __construct(
        private readonly array $options,
        private readonly string $requestType,
        private readonly array $requestConfig,
        private array $paramStorage
    ) {
    }

    public function getOptions(): array
    {
        return $this->options;
    }

    public function getRequestType(): string
    {
        return $this->requestType;
    }

    public function getRequestConfig(): array
    {
        return $this->requestConfig;
    }

    public function getParamStorage(): array
    {
        return $this->paramStorage;
    }

    public function getParamStorageByRequestType(string $requestType): array
    {
        return $this->paramStorage[$requestType];
    }

    public function addParamStorageByType(string $type, array $param): void
    {
        $this->paramStorage[$type] = $param;
    }

    public function hasParamStorageByRequestType(string $requestType): bool
    {
        return isset($this->paramStorage[$requestType]);
    }

    public function hasOptions(): bool
    {
        return !empty($this->options);
    }

    public function hasRequestConfig(): bool
    {
        return !empty($this->requestConfig);
    }

    public function hasParamStorage(): bool
    {
        return !empty($this->paramStorage);
    }
}
