<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\DTO;

final class RequestParameterContextDTO
{
    private array $dynamicParamStorage = [];

    public function __construct(
        private readonly array $options,
        private readonly string $requestType,
        private readonly array $requestConfig,
    ) {
    }

    public function getOptions(): array
    {
        return $this->options;
    }

    public function getRequestType(): string
    {
        return $this->requestType;
    }

    public function getRequestConfig(): array
    {
        return $this->requestConfig;
    }

    public function getDynamicParamStorage(): array
    {
        return $this->dynamicParamStorage;
    }

    public function getDynamicParamsByRequestType(string $requestType): array
    {
        return $this->dynamicParamStorage[$requestType] ?? [];
    }

    public function addDynamicParamsByType(string $type, array $params): void
    {
        $this->dynamicParamStorage[$type] = $params;
    }

    public function hasDynamicParamsByRequestType(string $requestType): bool
    {
        return isset($this->dynamicParamStorage[$requestType]) && !empty($this->dynamicParamStorage[$requestType]);
    }

    public function hasOptions(): bool
    {
        return !empty($this->options);
    }

    public function hasRequestConfig(): bool
    {
        return !empty($this->requestConfig);
    }

    public function hasDynamicParamStorage(): bool
    {
        return !empty($this->dynamicParamStorage);
    }
}
