<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\DTO;

use LoginAutonom\ExternalResourcesBundle\Interfaces\AuthConfigurationInterface;
use LoginAutonom\ExternalResourcesBundle\Interfaces\TransferConfigurationInterface;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;

#[Autoconfigure(autowire: false)]
final readonly class ApiConfigurationDTO implements TransferConfigurationInterface
{
    public function __construct(
        private string $protocol,
        private string $hostname,
        private ?string $path,
        private ?int $port,
        /**
         * @var AuthConfigurationInterface[]
         */
        private array $authConfigs,
        private array $options = [],
    ) {
    }

    public function getProtocol(): string
    {
        return $this->protocol;
    }

    public function hasProtocol(): bool
    {
        return isset($this->protocol);
    }

    public function getHostname(): string
    {
        return $this->hostname;
    }

    public function hasHostname(): bool
    {
        return isset($this->hostname);
    }

    public function getPort(): ?int
    {
        return $this->port;
    }

    public function hasPort(): bool
    {
        return isset($this->port);
    }

    public function getAuthConfigs(): array
    {
        return $this->authConfigs;
    }

    public function hasAuthConfigs(): bool
    {
        return isset($this->authConfigs) && $this->authConfigs !== [];
    }

    public function getOptions(): array
    {
        return $this->options;
    }

    public function hasOptions(): bool
    {
        return isset($this->options);
    }

    public function getPath(): ?string
    {
        return $this->path;
    }

    public function hasPath(): bool
    {
        return isset($this->path);
    }
}
