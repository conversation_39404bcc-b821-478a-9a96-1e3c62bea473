<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Enum;

enum ApiRequestParameterKeyEnum: string
{
    case TAX_NUMBER = 'tax_number';
    case VALID_FROM = 'valid_from';
    case VALID_TO = 'valid_to';
    case URL_PARAM = 'urlParam';
    case EXTRA_HEADERS = 'extraHeaders';
    case EXTRA_BODY = 'extraBody';
    case USE_HEADERS_FOR_DATES = 'useHeadersForDates';
}