<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Enum;

enum ApiRequestConfigurationKeyEnum: string
{
    case TYPE = 'type';
    case GROUP_BY = 'groupBy';
    case PARENT = 'parent';
    case CHILD = 'child';
    case PARAMS = 'params';
    case DYNAMIC_PARAMS = 'dynamicParams';
    case PARAMS_TO_EXTRACT = 'paramsToExtract';
    case FROM_REQUEST = 'fromRequest';
    case FIELD = 'field';
    case ORDER = 'order';
}
