<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Enum;

enum ApiExtractorConfigurationKeyEnum: string
{
    case REQUEST_CHAINS = 'requestChains';
    case REQUEST_TYPES = 'requestTypes';
    case EXTRA_FIELDS = 'extraFields';
    case FROM = 'from';
    case TO = 'to';
    case REQUEST_PARAMS = 'requestParams';
    case FIELD_MAPPINGS = 'fieldMappings';
    case OFFSET = 'offset';
}