<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Handler;

use LoginAutonom\CoreBundle\Iterator\ExtendByIndexedArrayIterator;
use LoginAutonom\CoreBundle\Util\ArrayUtil;
use LoginAutonom\ExternalResourcesBundle\DTO\ApiConfigurationDTO;
use LoginAutonom\ExternalResourcesBundle\DTO\ProcessingContextDTO;
use LoginAutonom\ExternalResourcesBundle\DTO\RequestParameterContextDTO;
use LoginAutonom\ExternalResourcesBundle\Interfaces\RequestHandlerInterface;
use LoginAutonom\ExternalResourcesBundle\Nexon\Builder\RequestParametersDTOBuilder;

use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\ApiRequestConfigurationKeyEnum;
use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\ApiExtractorConfigurationKeyEnum;
use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\ApiResponseValueEnum;
use LoginAutonom\ExternalResourcesBundle\Nexon\Guesser\RequestConfigParamsGuesser;
use LoginAutonom\ExternalResourcesBundle\Nexon\Guesser\DateParamGuesser;
use LoginAutonom\ExternalResourcesBundle\Nexon\Guesser\DynamicParamsGuesser;
use LoginAutonom\ExternalResourcesBundle\Nexon\Guesser\RequestSpecificParamsGuesser;
use LoginAutonom\ExternalResourcesBundle\Nexon\Util\FieldMappingUtil;
use LoginAutonom\ExternalResourcesBundle\Nexon\Util\ValueExtractionUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;
use Symfony\Component\DependencyInjection\Attribute\TaggedLocator;
use Symfony\Contracts\Service\ServiceProviderInterface;

#[Autoconfigure]
final class MultiRequestHandler implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    public function __construct(
        #[TaggedLocator(RequestHandlerInterface::TAG, defaultIndexMethod: 'type')]
        private readonly ServiceProviderInterface $requests,
        private readonly RequestParametersDTOBuilder $parameterBuilder,
        private readonly DateParamGuesser $dateParamGuesser,
        private readonly RequestSpecificParamsGuesser $requestSpecificParamsGuesser,
        private readonly RequestConfigParamsGuesser $requestConfigParamsGuesser,
        private readonly DynamicParamsGuesser $dynamicParamsGuesser,
        private readonly FieldMappingUtil $fieldMappingUtil,
        private readonly ArrayUtil $arrayUtil,
        private readonly ValueExtractionUtil $valueExtractionUtil,
    ) {
    }

    public function handle(
        array $requests,
        array $options,
        ApiConfigurationDTO $apiConfiguration
    ): array {
        $results = [];
        $sharedDynamicParams = [];
        $requests = $this->sortRequests($requests);
        foreach ($requests as $requestConfig) {
            $requestType = $requestConfig[ApiRequestConfigurationKeyEnum::TYPE->value] ?? $requestConfig;
            if (!$this->requests->has($requestType)) {
                $this->logger->warning('Request handler not found for type: {type}', ['type' => $requestType]);
                continue;
            }

            if (isset($results[$requestType])) {
                continue;
            }

            try {
                $processingContext = new ProcessingContextDTO(
                    $requestType, $requestConfig, $options, $apiConfiguration
                );

                $processedResults = $this->processRequest($processingContext, $sharedDynamicParams);

                $parent = $requestConfig[ApiRequestConfigurationKeyEnum::PARENT->value] ?? $requestType;
                $iterator = new ExtendByIndexedArrayIterator(
                    new \ArrayIterator($processedResults),
                    $results,
                    $parent
                );
                $processedResults = iterator_to_array($iterator);
                $results = $processedResults;
            } catch (\Exception $e) {
                $this->logger?->error('Error processing request type {type}: {message}', [
                    'type' => $requestType,
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
                throw $e;
            }
        }

        return $results;
    }

    private function processRequest(ProcessingContextDTO $context, array &$sharedDynamicParams): array
    {
        /** @var RequestHandlerInterface $request */
        $request = $this->requests->get($context->getRequestType());

        $parametersContext = new RequestParameterContextDTO(
            $context->getOptions(),
            $context->getRequestType(),
            $context->getRequestConfig()
        );

        foreach ($sharedDynamicParams as $type => $params) {
            $parametersContext->addDynamicParamsByType($type, $params);
        }

        $requestParams = $this->getRequestParams($parametersContext);
        $requestRawData = $request->handle($context->getApiConfiguration(), $requestParams);
        $processedData = $this->processResponseData($requestRawData);
        $processedData = $this->renameMapping($processedData, $context);
        $this->extractDynamicParams($parametersContext, $processedData, $sharedDynamicParams);

        if (isset($context->getRequestConfig()[ApiRequestConfigurationKeyEnum::GROUP_BY->value])) {
            $groupByKey = $context->getRequestConfig()[ApiRequestConfigurationKeyEnum::GROUP_BY->value];
            $processedData = ArrayUtil::groupBy(
                $processedData,
                fn($item) => $item[$groupByKey] ?? null
            );
        }

        return $processedData;
    }

    private function processResponseData(mixed $rawData): array
    {
        if (empty($rawData)) {
            return [];
        }

        $data = is_string($rawData)
            ? json_decode($rawData, true) ?? []
            : (is_array($rawData) ? $rawData : []);

        return $data[ApiResponseValueEnum::VALUE->value] ?? $data;
    }

    private function getRequestParams(RequestParameterContextDTO $context): array
    {
        return $this->parameterBuilder
            ->reset()
            ->setDateParameters(
                $this->dateParamGuesser->guess(
                    $context->getRequestConfig()
                )
            )
            ->setRequestSpecificParameters(
                $this->requestSpecificParamsGuesser->guess(
                    $context->getRequestType(),
                    $context->getOptions()
                )
            )
            ->setRequestConfigParameters(
                $this->requestConfigParamsGuesser->guess(
                    $context->getRequestConfig()
                )
            )
            ->setDynamicParameters(
                $this->dynamicParamsGuesser->guess(
                    $context
                )
            )
            ->build()
            ->toArray();
    }

    private function sortRequests(array $requests): array
    {
        return ArrayUtil::sortByKey($requests, ApiRequestConfigurationKeyEnum::ORDER->value, 'ASC');
    }

    private function renameMapping(array $processedData, ProcessingContextDTO $context): array
    {
        $requestType = $context->getRequestType();
        $options = $context->getOptions();

        if (isset($options[ApiExtractorConfigurationKeyEnum::FIELD_MAPPINGS->value][$requestType])) {
            $mappings = $options[ApiExtractorConfigurationKeyEnum::FIELD_MAPPINGS->value][$requestType];
            return $this->fieldMappingUtil->applyFieldMappings($processedData, $mappings);
        }

        return $processedData;
    }

    private function extractDynamicParams(
        RequestParameterContextDTO $parametersContext,
        array $processedData,
        array &$sharedDynamicParams
    ): void {
        $requestType = $parametersContext->getRequestType();
        $requestConfig = $parametersContext->getRequestConfig();

        if ($parametersContext->hasDynamicParamsByRequestType($requestType)) {
            return;
        }

        $fieldsToExtract = $requestConfig[ApiRequestConfigurationKeyEnum::PARAMS_TO_EXTRACT->value] ?? [];
        if (empty($fieldsToExtract)) {
            return;
        }

        $newDynamicParams = $this->collectDynamicParams($fieldsToExtract, $processedData);

        if (empty($newDynamicParams)) {
            return;
        }

        // Store in both local context and shared array
        $parametersContext->addDynamicParamsByType($requestType, $newDynamicParams);
        $sharedDynamicParams[$requestType] = $newDynamicParams;
    }

    private function collectDynamicParams(array $fields, array $processedData): array
    {
        $uniqueFields = array_unique($fields);
        $dynamicParams = [];

        foreach ($uniqueFields as $field) {
            $values = $this->valueExtractionUtil->extract($processedData, $field);
            if (!empty($values)) {
                $dynamicParams[$field] = array_values($values);
            }
        }

        return $dynamicParams;
    }
}
