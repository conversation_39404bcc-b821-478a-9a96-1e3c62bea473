<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\DTO;

final class RequestDynamicParamsDTO
{
    private array $dynamicParams;

    public function __construct(array $dynamicParams)
    {
        $this->dynamicParams = $dynamicParams;
    }

    public function getDynamicParams(): array
    {
        return $this->dynamicParams;
    }

    public function hasDynamicParams(): bool
    {
        return isset($this->dynamicParams);
    }

    public function addDynamicParamsByRequestType(string $requestType, array $dynamicParams): void
    {
        $this->dynamicParams[$requestType] = $dynamicParams;
    }
}