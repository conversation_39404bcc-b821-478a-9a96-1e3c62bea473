<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\DTO;

final readonly class RequestParametersDTO
{
    public function __construct(
        private array $dateParameters = [],
        private array $requestSpecificParameters = [],
        private array $requestConfigParameters = [],
        private array $dynamicParameters = [],
    ) {
    }

    public function getDateParameters(): array
    {
        return $this->dateParameters;
    }

    public function hasDateParameters(): bool
    {
        return !empty($this->dateParameters);
    }

    public function getRequestSpecificParameters(): array
    {
        return $this->requestSpecificParameters;
    }

    public function hasRequestSpecificParameters(): bool
    {
        return !empty($this->requestSpecificParameters);
    }

    public function getRequestConfigParameters(): array
    {
        return $this->requestConfigParameters;
    }

    public function hasRequestConfigParameters(): bool
    {
        return !empty($this->requestConfigParameters);
    }

    public function getDynamicParameters(): array
    {
        return $this->dynamicParameters;
    }

    public function hasDynamicParameters(): bool
    {
        return !empty($this->dynamicParameters);
    }

    public function toArray(): array
    {
        return array_merge(
            $this->dateParameters,
            $this->requestSpecificParameters,
            $this->requestConfigParameters,
            $this->dynamicParameters
        );
    }
}
