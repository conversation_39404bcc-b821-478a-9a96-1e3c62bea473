<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Builder;

use LoginAutonom\ExternalResourcesBundle\Nexon\DTO\RequestParametersDTO;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;

#[Autoconfigure(shared: false)]
final class RequestParametersDTOBuilder
{
    private array $dateParameters;
    private array $requestSpecificParameters;
    private array $requestConfigParameters;
    private array $dynamicParameters;


    public function build(): RequestParametersDTO
    {
        return new RequestParametersDTO(
            dateParameters: $this->dateParameters ?? [],
            requestSpecificParameters: $this->requestSpecificParameters ?? [],
            requestConfigParameters: $this->requestConfigParameters ?? [],
            dynamicParameters: $this->dynamicParameters ?? [],
        );
    }

    public function reset(): self
    {
        unset(
            $this->dateParameters,
            $this->requestSpecificParameters,
            $this->requestConfigParameters,
            $this->dynamicParameters
        );

        return $this;
    }

    public function setDateParameters(array $dateParameters): self
    {
        $this->dateParameters = $dateParameters;

        return $this;
    }

    public function setRequestSpecificParameters(array $requestSpecificParameters): self
    {
        $this->requestSpecificParameters = $requestSpecificParameters;

        return $this;
    }

    public function setRequestConfigParameters(array $requestConfigParameters): self
    {
        $this->requestConfigParameters = $requestConfigParameters;

        return $this;
    }

    public function setDynamicParameters(array $dynamicParameters): self
    {
        $this->dynamicParameters = $dynamicParameters;

        return $this;
    }
}
