<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Guesser;

use LoginAutonom\ExternalResourcesBundle\DTO\RequestParameterContextDTO;
use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\ApiRequestConfigurationKeyEnum;

final readonly class DynamicParamsGuesser
{
    public function guess(RequestParameterContextDTO $context): array
    {
        $requestConfig = $context->getRequestConfig();

        if (!isset($requestConfig[ApiRequestConfigurationKeyEnum::DYNAMIC_PARAMS->value])) {
            return [];
        }

        $dynamicParameters = [];
        $dynamicParams = $requestConfig[ApiRequestConfigurationKeyEnum::DYNAMIC_PARAMS->value];

        foreach ($dynamicParams as $paramKey => $sourceConfig) {
            $sourceRequest = $sourceConfig[ApiRequestConfigurationKeyEnum::FROM_REQUEST->value];
            $sourceField = $sourceConfig[ApiRequestConfigurationKeyEnum::FIELD->value];

            if (!$context->hasParamStorageByRequestType($sourceRequest)) {
                continue;
            }

            if (!isset($context->getParamStorageByRequestType($sourceRequest)[$sourceField])) {
                continue;
            }

            $dynamicParameters[$paramKey] = $context->getParamStorageByRequestType($sourceRequest)[$sourceField];
        }

        return $dynamicParameters;
    }
}