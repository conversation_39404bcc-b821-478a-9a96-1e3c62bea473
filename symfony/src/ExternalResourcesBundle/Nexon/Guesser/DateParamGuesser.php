<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Guesser;

use LoginAutonom\CoreBundle\Enum\DateRangePatternEnum;
use LoginAutonom\CoreBundle\Guesser\DateRangeGuesser;
use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\ApiExtractorConfigurationKeyEnum;

final readonly class DateParamGuesser
{
    public function __construct(
        private DateRangeGuesser $dateRangeGuesser,
    ) {
    }

    public function guess(array $requestConfig): array
    {
        $dateParameters = [];

        if (isset($requestConfig[DateRangePatternEnum::DATE_PATTERN->value])) {
            $datePattern = $this->dateRangeGuesser->guess($requestConfig);
            $dateParameters = [
                ApiExtractorConfigurationKeyEnum::FROM->value => $datePattern[DateRangePatternEnum::FROM->value],
                ApiExtractorConfigurationKeyEnum::TO->value => $datePattern[DateRangePatternEnum::TO->value]
            ];
        } else {
            if (isset($requestConfig[ApiExtractorConfigurationKeyEnum::FROM->value])) {
                $dateParameters[ApiExtractorConfigurationKeyEnum::FROM->value] =
                    $requestConfig[ApiExtractorConfigurationKeyEnum::FROM->value];
            }

            if (isset($requestConfig[ApiExtractorConfigurationKeyEnum::TO->value])) {
                $dateParameters[ApiExtractorConfigurationKeyEnum::TO->value] =
                    $requestConfig[ApiExtractorConfigurationKeyEnum::TO->value];
            }
        }

        return $dateParameters;
    }
}