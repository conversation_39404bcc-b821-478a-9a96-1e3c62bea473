<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Guesser;

use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\ApiExtractorConfigurationKeyEnum;

final readonly class RequestSpecificParamsGuesser
{
    public function guess(string $requestType, array $options): array
    {
        if (isset($options[ApiExtractorConfigurationKeyEnum::REQUEST_PARAMS->value][$requestType])) {
            return $options[ApiExtractorConfigurationKeyEnum::REQUEST_PARAMS->value][$requestType];
        }

        return [];
    }
}