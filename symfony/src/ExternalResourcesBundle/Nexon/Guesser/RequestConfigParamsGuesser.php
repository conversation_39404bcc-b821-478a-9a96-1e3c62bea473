<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Guesser;

use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\ApiRequestConfigurationKeyEnum;

final readonly class RequestConfigParamsGuesser
{
    public function guess(array $requestConfig): array
    {
        if (isset($requestConfig[ApiRequestConfigurationKeyEnum::PARAMS->value])) {
            return $requestConfig[ApiRequestConfigurationKeyEnum::PARAMS->value];
        }

        return [];
    }
}