<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Request;

use LoginAutonom\ExternalResourcesBundle\Builder\APIAdapterBuilder;
use LoginAutonom\ExternalResourcesBundle\Builder\RequestDataBuilder;
use LoginAutonom\ExternalResourcesBundle\DTO\ApiConfigurationDTO;
use LoginAutonom\ExternalResourcesBundle\Enum\HttpMethodEnum;
use LoginAutonom\ExternalResourcesBundle\Interfaces\RequestHandlerInterface;
use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\ApiRequestParameterKeyEnum;
use LoginAutonom\ExternalResourcesBundle\Nexon\Interface\ApiRequestInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

final class HrRelationshipRequest implements RequestHandlerInterface, ApiRequestInterface, LoggerAwareInterface
{
    use LoggerAwareTrait;

    public const TARGET_PATH = '/OdataApi/v5.0/HrRelationship';

    public function __construct(
        private readonly APIAdapterBuilder $adapterBuilder,
        private readonly RequestDataBuilder $requestDataBuilder,
    ) {
    }

    public function handle(ApiConfigurationDTO $apiConfigurationDTO, array $params = []): mixed
    {
        try {
            $requestBody = $this->buildRequestBody($params);
            $requestHeaders = $this->buildRequestHeaders($params);
            $adapter = $this->adapterBuilder->reset()
                ->setApiConfigurationDTO($apiConfigurationDTO)
                ->build();
            $requestDataDTO = $this->requestDataBuilder->reset()
                ->setPath(self::TARGET_PATH)
                ->setMethod(HttpMethodEnum::GET)
                ->setHeaders($requestHeaders)
                ->setBodyData($requestBody)
                ->setApiConfigurationDTO($apiConfigurationDTO)
                ->build();

            $result = $adapter->get($requestDataDTO, $apiConfigurationDTO->getOptions());
            return (string) $result->getBody();
        } catch (\Throwable $e) {
            $this->logger->error('Error in HrRelationshipRequest: {message}', [
                'message' => $e->getMessage(),
                'params' => $params,
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }

    public function buildRequestBody(array $params = []): array
    {
        $body = [];

        if (
            isset($params[ApiRequestParameterKeyEnum::EXTRA_BODY->value]) &&
            is_array($params[ApiRequestParameterKeyEnum::EXTRA_BODY->value])
        ) {
            $body = array_merge($body, $params[ApiRequestParameterKeyEnum::EXTRA_BODY->value]);
        }

        return $body;
    }

    public function buildRequestHeaders(array $params = []): array
    {
        $headers = [];
        if (
            isset($params[ApiRequestParameterKeyEnum::EXTRA_HEADERS->value]) &&
            is_array($params[ApiRequestParameterKeyEnum::EXTRA_HEADERS->value])
        ) {
            $headers = array_merge($headers, $params[ApiRequestParameterKeyEnum::EXTRA_HEADERS->value]);
        }

        return $headers;
    }

    public static function type(): string
    {
        return 'hr-relationship';
    }
}
