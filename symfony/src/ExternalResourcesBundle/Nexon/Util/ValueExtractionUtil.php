<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Util;

final class ValueExtractionUtil
{
    public function extract(array $results, string $field): array
    {
        $values = [];
        
        array_walk_recursive($results, function ($value, $key) use ($field, &$values) {
            if ($key === $field) {
                $values[] = $value;
            }
        });

        return array_unique($values);
    }
}
