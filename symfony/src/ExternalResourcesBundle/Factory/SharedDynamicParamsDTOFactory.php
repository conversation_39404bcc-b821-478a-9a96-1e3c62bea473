<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Factory;

use LoginAutonom\ExternalResourcesBundle\DTO\SharedDynamicParamsDTO;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;

#[Autoconfigure]
final readonly class SharedDynamicParamsDTOFactory
{
    public function create(): SharedDynamicParamsDTO
    {
        return new SharedDynamicParamsDTO();
    }
}
