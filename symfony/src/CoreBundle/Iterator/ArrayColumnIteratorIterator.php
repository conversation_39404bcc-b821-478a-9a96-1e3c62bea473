<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\Iterator;

final readonly class ArrayColumnIteratorIterator implements \Iterator, \Countable
{
    public function __construct(
        private iterable $innerIterator,
        private string $columnName
    ) {
    }

    public function current(): mixed
    {
        return array_column($this->innerIterator->current(), $this->columnName);
    }

    public function next(): void
    {
        $this->innerIterator->next();
    }

    public function key(): mixed
    {
        return $this->innerIterator->key();
    }

    public function valid(): bool
    {
        return $this->innerIterator->valid();
    }

    public function rewind(): void
    {
        $this->innerIterator->rewind();
    }

    public function count(): int
    {
        return count($this->innerIterator);
    }
}
