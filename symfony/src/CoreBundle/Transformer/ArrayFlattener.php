<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\Transformer;

final class ArrayFlattener
{
    public function flat(array $original): array
    {
        $newArray = new \ArrayObject([]);
        foreach ($original as $key => $item) {
            if (!is_array($item)) {
                $newArray[$key] = $item;
                continue;
            }
            $this->doFlat($newArray, $key, $item);
        }

        return $newArray->getArrayCopy();
    }

    private function doFlat(\ArrayObject $original, string $key, array $item): void
    {
        foreach ($item as $subKey => $subItem) {
            if (is_scalar($subItem) || $subItem === null) {
                $original["{$key}.{$subKey}"] = $subItem;
                continue;
            }
            $this->doFlat($original, "{$key}.{$subKey}", $subItem);
        }
    }
}
