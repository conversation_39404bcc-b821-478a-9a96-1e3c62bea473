<?php

declare(strict_types=1);

namespace Components\ProcessAcknowledgment\Provider;

use Components\ProcessAcknowledgment\Descriptor\ProcessAcknowledgmentDescriptor;

final class EmployeeExtraHoursChangesProvider
{
    public function __invoke(array $employeeContractIds, ProcessAcknowledgmentDescriptor $descriptor): array
    {
        $criteria = new \CDbCriteria();
        $criteria->alias = 'eeh';
        $criteria->condition = '
                day >= :validFrom
            AND day <= :validTo
            AND employee_contract_id IN ("' . implode('","', $employeeContractIds) . '")
            AND inside_type = :insideStandbyType
        ';

        $criteria->params = [
            ':validFrom' => $descriptor->getValidFrom(),
            ':validTo' => $descriptor->getValidTo(),
            ':insideStandbyType' => \EmployeeExtraHours::INSIDE_STANDBY,
        ];

        $criteria->addCondition('IFNULL(eeh.modified_on, eeh.created_on) = (
            SELECT
                MAX(IFNULL(t2.modified_on, t2.created_on))
            FROM employee_extra_hours t2
            WHERE 
                t2.employee_contract_id = eeh.employee_contract_id
                AND t2.day = eeh.day
                AND t2.status = eeh.status
        )');

        $criteria->group = 'employee_contract_id, day, status';
        $criteria->order = 'employee_contract_id, day, status';

        return \EmployeeExtraHours::model()->findAll($criteria);
    }
}