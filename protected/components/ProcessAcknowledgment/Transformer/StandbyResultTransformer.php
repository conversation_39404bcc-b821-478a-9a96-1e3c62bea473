<?php

namespace Components\ProcessAcknowledgment\Transformer;

use Components\Employee\Enum\EmployeeMainDataEnum;
use Components\ProcessAcknowledgment\Descriptor\ProcessAcknowledgmentDescriptor;
use Components\ProcessAcknowledgment\Enum\ProcessAcknowledgmentEnum;
use Components\ProcessAcknowledgment\Helper\ProcessAcknowledgmentHelper;

final class StandbyResultTransformer
{
    public function transform(ProcessAcknowledgmentDescriptor $descriptor, array $standbys, array $standbysIndexedRowIds, string $employeeContractId, bool $filterInTable = false): array
    {
        $standbys = array_filter($standbys, function ($row) use ($employeeContractId) {
            return $row['employee_contract_id'] === $employeeContractId;
        });

        if (empty($standbysIndexedRowIds) && empty($standbys)) {
            return [];
        }

        $filteredStandbys = $this->filterProcessedData($standbys, $descriptor, $employeeContractId, $filterInTable);

        $results = [];
        $processedDays = [];
        foreach ($filteredStandbys as $standby) {
            if (isset($processedDays[$standby['day']]) || !in_array($standby['status'], [\Status::PUBLISHED, \Status::DELETED])) {
                continue;
            }

            $originalValue = '';
            $newValue = $this->getStandbyInfo($standby);
            $requestId = \Dict::getValue('workSchedule_modifiy');

            $matchingPreRow = null;

            if ($standby['status'] == \Status::PUBLISHED) {
                $matchingPreRow = array_filter($standbysIndexedRowIds, function ($row) use ($standby) {
                    return $row['day'] == $standby['day'] && $row['status'] === \Status::DELETED;
                });

                if (empty($matchingPreRow)) {
                    $matchingPreRow = array_filter($standbysIndexedRowIds, function ($row) use ($standby) {
                        return $row['day'] == $standby['day'] && $row['status'] === \Status::INVALID;
                    });
                }

                if (!empty($matchingPreRow)) {
                    $matchingPreRow = reset($matchingPreRow);
                    $originalValue = $this->getStandbyInfo($matchingPreRow);
                }
            } elseif ($standby['status'] == \Status::DELETED) {
                $matchingPreRow = array_filter($standbysIndexedRowIds, function ($row) use ($standby) {
                    return $row['day'] == $standby['day'] && $row['status'] === \Status::INVALID;
                });

                if (!empty($matchingPreRow)) {
                    $originalValue = $this->getStandbyInfo(reset($matchingPreRow));
                    $newValue = '';
                }
            }

            $results[$employeeContractId][] = $this->collectStandbyData($standby, $requestId, $originalValue, $newValue);

            $processedDays[$standby['day']] = true;
        }

        return $results;
    }

    private function filterProcessedData(array $standbys, ProcessAcknowledgmentDescriptor $descriptor, string $employeeContractId, bool $useFilter): array
    {
        if (!$useFilter) {
            return $standbys;
        }

        return array_filter($standbys, function ($row) use ($descriptor, $employeeContractId) {
            return !array_filter($descriptor->getProcessAcknowledgments(), function ($acknowledgment) use ($row, $employeeContractId) {
                return $row['employee_contract_id'] == $employeeContractId
                    && $row['day'] == $acknowledgment['day']
                    && ProcessAcknowledgmentEnum::IDENTIFIER_EXTRA_HOURS_STANDBY == $acknowledgment['process_id'];
            });
        });
    }

    private function collectStandbyData($standby, $requestId, $originalValue, $newValue): array
    {
        return [
            EmployeeMainDataEnum::EMPLOYEE_CONTRACT_ID => $standby[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_ID],
            ProcessAcknowledgmentEnum::REQUEST_IDENTIFIER => ProcessAcknowledgmentEnum::IDENTIFIER_EXTRA_HOURS_STANDBY . '_' . $standby['row_id'],
            ProcessAcknowledgmentEnum::REQUEST_ID => $requestId,
            ProcessAcknowledgmentEnum::AFFECTED_DAY => $standby['day'],
            ProcessAcknowledgmentEnum::ORIGINAL_VALUE => $originalValue,
            ProcessAcknowledgmentEnum::NEW_VALUE => $newValue,
            ProcessAcknowledgmentEnum::MODIFIED_BY => ProcessAcknowledgmentHelper::getLastModifierByUserID(
                $standby[ProcessAcknowledgmentEnum::MODIFIED_BY],
                $standby[ProcessAcknowledgmentEnum::CREATED_BY]
            ),
            ProcessAcknowledgmentEnum::OPERATION_DATE => ProcessAcknowledgmentHelper::getLastModifierDate(
                $standby[ProcessAcknowledgmentEnum::MODIFIED_ON],
                $standby[ProcessAcknowledgmentEnum::CREATED_ON]
            )
        ];
    }

    private function getStandbyInfo(array $standby): string
    {
        return sprintf(
            '%s: %s-%s',
            \Dict::getModuleValue('ttwa-wfm', 'standby'),
            $standby['start'],
            $standby['end']
        );
    }
}