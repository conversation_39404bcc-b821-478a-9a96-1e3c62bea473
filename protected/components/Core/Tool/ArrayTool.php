<?php

declare(strict_types=1);

namespace Components\Core\Tool;

final class ArrayTool
{
    public function __construct()
    {
    }

    public function indexBy(array $source, string $key): array
    {
        return array_column($source, null, $key);
    }

    public function groupByKey(array $source, string $key): array
    {
        $group = [];
        foreach ($source as $item) {
            $group[$item[$key]][] = $item;
        }

        return $group;
    }
}