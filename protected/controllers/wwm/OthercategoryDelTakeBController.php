<?php

Yang::import('application.components.wwm.Grid2WhmDeliveryTakeBack.controllers.WhmDeliveryTakeBack.WhmDeliveryTakeBackCtrl');

class OthercategoryDelTakeBController extends WhmDeliveryTakeBackCtrl
{
	private $mainCategories;

    /**
     * Constuctor override
     */
	public function __construct() {
		$this->mainCategories =
		[
            ["id" => "whm-mc-card",  "value" => Dict::getValue("whmMCCard")],
            ["id" => "whm-mc-hr",       "value" => Dict::getValue("whmMCHr")]
        ];
		parent::sortDatas($this->mainCategories);
		parent::__construct("wwm/othercategoryDelTakeB");
	}

    /**
     * G2BInit override
     * @return void
     */
	protected function G2BInit() {
		Yang::setSessionValue("whm_delivery_take_back_warehouse_dict", "menu_item_other_category");
		parent::setControllerPageTitleId("page_title_other_category_del_takeb", "ttwa-wwm");
		parent::setExportFileName(Dict::getValue("export_file_other_category_del_takeb"));
		parent::G2BInit();
	}

	/**
	 * Where override
	 * @param array $filter
	 * @return array
	 */
	protected function getColsAndTable($filter)
	{
		$ret			= parent::getColsAndTable($filter);
		$cats			= [];
		foreach ($this->mainCategories as $cat) {
			$cats[] = $cat["id"] ?? "";
		}
		$ret["where"]	= " AND `main_category_id` IN ('" . implode("', '", $cats) . "')";
		return $ret;
	}

	/**
	 * Columns override
	 * @return array
	 */
	public function columns()
	{
		$cols															= parent::columns();
		$cols["deliverGrid"]["main_category_id"]["options"]["array"]	= $this->mainCategories;
		$cols["deliverGrid"]["main_category_id"]["onchange"][]			= "whmSerialNumber";
		$cols["deliverGrid"]["sub_category_id"]["onchange"]				= ["whmSerialNumber"];
		$cols["deliverGrid"]["whmSerialNumber"]						=
		[
			'grid'		=> true,
			'col_type'	=> 'combo',
			'export'	=> true,
			'options'	=> [
				'mode'		=> Grid2Controller::G2BC_QUERY_MODE_SQL,
				'sql'		=> "
					SELECT
						`serial_number` AS id,
						`serial_number` AS value
					FROM `warehouse_othercategory`
					WHERE
							`status` = " . Status::PUBLISHED . "
						AND `sub_category_id` = '{sub_category_id}'
						AND `device_status` = 2
				"
			],
			'mandatory'	=> true,
			'width' 	=> 250
		];
		return $cols;
	}

	/**
	 * Labels override
	 * @return array
	 */
	public function attributeLabels() {
		$labels = parent::attributeLabels();
		$labels["deliverGrid"]["whmSerialNumber"] = Dict::getModuleValue("ttwa-wwm", "whmSerialNumber");
		return $labels;
	}

	/**
	 * Delivery override
	 * @param string $modelName
	 * @param array $uniqueFieldsInDb
	 * @return void
	 */
	public function actionDeliver($modelName = "", $uniqueFieldsInDb = []) {
		parent::actionDeliver("Othercategory", ["whmSerialNumber" => "serial_number", "sub_category_id" => "sub_category_id"]);
	}

	/**
	 * Takeback override
	 * @param string $modelName
	 * @param array $uniqueFieldsInDb
	 * @return void
	 */
	public function actionTakeBackById($modelName = "", $uniqueFieldsInDb = []) {
		parent::actionTakeBackById("Othercategory", ["whmSerialNumber" => "serial_number", "sub_category_id" => "sub_category_id"]);
	}

	/**
	 * Delete override
	 * @param string $modelName
	 * @param bool $hasRight
	 * @param array $uniqueFieldsInDb
	 * @return void
	 */
	public function actionDelete($modelName = null, $hasRight = false, $uniqueFieldsInDb = []) {
		parent::actionDelete("Othercategory", false, ["whmSerialNumber" => "serial_number", "sub_category_id" => "sub_category_id"]);
	}
}