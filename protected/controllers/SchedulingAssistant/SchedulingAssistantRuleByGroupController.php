<?php

class SchedulingAssistantRuleByGroupController extends Grid2Controller
{

	private $publishedStatus;
	private $defaultEnd;
	private $dayTypeGroup;
	private $competency;
	private $workgroup;
	private $ruleIByGeneratedFrom;

	public function __construct()
	{
		$this->publishedStatus = Status::PUBLISHED;
		$this->defaultEnd = App::getSetting('defaultEnd');
		$this->ruleIByGeneratedFrom = [
			'shiftgroupRuleWithWorkgroup'	=> 'shiftgroup-rule-with-workgroup',
			'shiftgroupRuleWithWorkgroups'	=> 'shiftgroup-rule-with-workgroups',
			'twoCompetencyDoNotWorkInOneShiftGroup' => 'two-competency-do-not-work-in-one-shift-group',
		];
		parent::__construct("SchedulingAssistant/schedulingAssistantRuleByGroup");
		$this->uploadDayTypeGroup();
		$this->uploadCompetency();
		$this->uploadWorkgroup();
	}

	protected function uploadcompetency()
	{
		$competency = new Competency();
		$this->competency =  $competency->getAllData();
	}

	protected function uploadDayTypeGroup()
	{
		$daytypeGroup = new DaytypeGroup;
		$this->dayTypeGroup = $daytypeGroup->getAllData();
	}

	protected function uploadWorkgroup()
	{
		$workgroup = new Workgroup;
		$this->workgroup = $workgroup->getAllData();
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("SchedulingAssistantRuleByGroup", "dhtmlxGrid");
		parent::setControllerPageTitleId("page_title_SchedulingAssistantRuleByGroup");
		parent::enableMultiGridMode();

		$this->LAGridRights->overrideInitRights("paging", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search_header", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("select", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("column_move", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload_sortings", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("col_sorting", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("modify", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("delete", true, "dhtmlxGrid");

		$this->LAGridRights->overrideInitRights("details", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("multi_select", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search", false, "dhtmlxGrid");

		$path = Yang::addAsset(Yang::getAlias('application.assets.SchedulingAssistant'), false, -1, true);
		Yang::registerScriptFile($path . '/js/SchedulingAssistantRuleByGroup.js');

		parent::G2BInit();
	}

	public function columns()
	{
		$today = date("Y-m-d");

		$ruleIdOptions = App::getLookup('SchedulingAssistantRuleId');
		$ruleTypeOptions = App::getLookup('SchedulingAssistantRuleType');

		$art = new ApproverRelatedGroup;
		$gargCompanySQL = $art->getApproverReleatedGroupSQL("Company", "companyMainData", false, "'{$today}'", "AND", $today);

		$company = new Company();
		$companyCriteriaGrid = $company->getColumnGridCriteria($gargCompanySQL['where'], $today);

		$enableCompanyAll = (!App::getSetting("useCompanyAndPayrollRights"))
						&& (strpos($gargCompanySQL['where'], "/*approverRelatedGroup is forbiden!*/") !== false);

		$columns["dhtmlxGrid"] =
			[
				'group_value'	=>
					[
						'width' => 150, 'align' => 'left', 'col_type' => 'combo', 'grid' => true, 'export' => true,  'window' => false,
						'options'	=>
							[
								'mode'					            => Grid2Controller::G2BC_QUERY_MODE_MODEL,
								'modelSelectionModel'   	        => $company,
								'modelSelectionCriteriaGridMode'    => $companyCriteriaGrid,
								'modelSelectionCriteriaDialogMode'  => $companyCriteriaGrid,
								'comboId'				            => 'company_id',
								'comboValue'			            => 'company_name',
								'array' => ($enableCompanyAll)?[["id"=>"ALL","value"=>Dict::getValue("all")]]:"",
							],
					],
				'rule_name'		=> ['width' => 200, 'align' => 'left', 'col_type' => 'ed', 'grid' => true, 'export' => true],
				'rule_id'		=>
					[
						'width' => 300, 'align' => 'left', 'export' => false, 'col_type' => 'combo',
						'options'		=>
							[
								'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
								'array'	=> $ruleIdOptions
							],

					],
				'rule_type'		=>
					[
						'width' => 100, 'align' => 'center', 'export' => false, 'col_type' => 'combo',
						'options'		=>
							[
								'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
								'array'	=> $ruleTypeOptions
							],
					],
				'rule_setting'	=> ['width' => 500, 'align' => 'left', 'col_type' => 'ro', 'grid' => true, 'export' => true, 'window' => false],
				'coeff'			=> ['width' => 50, 'align' => 'center', 'col_type' => 'ro', 'grid' => true, 'export' => true, 'window' => false],
				'note'			=> ['width' => 200, 'align' => 'left', 'col_type' => 'ro', 'grid' => true, 'export' => true, 'window' => false],
				'workgroup_id'	=> ['width' => 200, 'align' => 'left', 'col_type' => 'ro', 'grid' => false, 'export' => false, 'window' => false],
			];

		$art = new ApproverRelatedGroup;
		$gargWorkgroupSQL = $art->getApproverReleatedGroupSQL("Workgrooup", "companyMainData", false, "'{ $today}'", "AND", $today);

		$workgroup = new Workgroup();
		$workgroupCriteriaGrid = $workgroup->getColumnGridCriteria($gargWorkgroupSQL['where'], $today);

		$daytypeGroup = new DaytypeGroup;
		$daytypeGroupCriteriaGrid = $daytypeGroup->getColumnGridCriteria();

		$columns["shiftgroupRuleWithWorkgroup"] = [
			'group_value'	=> [
				'width' => 150, 'align' => 'left', 'col_type' => 'combo', 'grid' => true, 'export' => true,  'window' => true,
				'options'	=> [
						'mode'					            => Grid2Controller::G2BC_QUERY_MODE_MODEL,
						'modelSelectionModel'   	        => $company,
						'modelSelectionCriteriaGridMode'    => $companyCriteriaGrid,
						'modelSelectionCriteriaDialogMode'  => $companyCriteriaGrid,
						'comboId'				            => 'company_id',
						'comboValue'			            => 'company_name',
						'array' => ($enableCompanyAll)?[["id"=>"ALL","value"=>Dict::getValue("all")]]:"",
				],
			],
			'rule_name'		=> ['width' => 200, 'align' => 'left', 'col_type' => 'ed', 'grid' => true, 'export' => true],
			'rule_type'		=>
				[
					'width' => 100, 'align' => 'center', 'export' => false, 'col_type' => 'combo',
					'options'		=>
						[
							'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
							'array'	=> $ruleTypeOptions
						],
				],
			'shiftgroup_id'	=> 	['width' => 200, 'col_type' => 'combo', 'grid' => true, 'export' => false,
				'from_json' => true,
				'default_value' => '',
				'options'	=>	[
					'mode'								=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
					'modelSelectionModel'   	        => $daytypeGroup,
					'modelSelectionCriteriaGridMode'    => $daytypeGroupCriteriaGrid,
					'modelSelectionCriteriaDialogMode'  => $daytypeGroupCriteriaGrid,
					'comboId'				            => 'daytype_group_id',
					'comboValue'			            => 'daytype_group_name',
					'array'								=> [["id" => "", "value" => ""]]
				],
			],
			'workgroup_id'	=> 	['width' => 200, 'col_type' => 'combo', 'grid' => true, 'export' => false,
				'from_json' => true,
				'default_value'	=> '',
				'options'		=>	[
					'mode'								=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
					'modelSelectionModel'   	        => $workgroup,
					'modelSelectionCriteriaGridMode'    => $workgroupCriteriaGrid,
					'modelSelectionCriteriaDialogMode'  => $workgroupCriteriaGrid,
					'comboId'				            => 'workgroup_id',
					'comboValue'			            => 'workgroup_name',
					'array'								=> [["id" => "", "value" => ""]]
				],
			],
			'minimum' 	=> ['width' => 80,	'col_type' => 'ed', 'grid' => false, 'export' => false, 'from_json' => true],
			'maximum' 	=> ['width' => 80,	'col_type' => 'ed', 'grid' => false, 'export' => false, 'from_json' => true],
			'optimum' 	=> ['width' => 80,	'col_type' => 'ed', 'grid' => false, 'export' => false, 'from_json' => true],
			'coeff' 	=> ['width' => 80,	'col_type' => 'ed', 'grid' => false, 'export' => false, 'window' => true],
			'note'		=> ['width' => 200, 'col_type' => 'ed', 'grid' => false, 'export' => false, 'window' => true],
		];

		$columns["shiftgroupRuleWithWorkgroups"] = [
			'group_value'	=> [
				'width' => 150, 'align' => 'left', 'col_type' => 'combo', 'grid' => true, 'export' => true,  'window' => true,
				'options'	=> [
					'mode'					            => Grid2Controller::G2BC_QUERY_MODE_MODEL,
					'modelSelectionModel'   	        => $company,
					'modelSelectionCriteriaGridMode'    => $companyCriteriaGrid,
					'modelSelectionCriteriaDialogMode'  => $companyCriteriaGrid,
					'comboId'				            => 'company_id',
					'comboValue'			            => 'company_name',
					'array' => ($enableCompanyAll)?[["id"=>"ALL","value"=>Dict::getValue("all")]]:"",
				],
			],
			'rule_name'		=> ['width' => 200, 'align' => 'left', 'col_type' => 'ed', 'grid' => true, 'export' => true],
			'rule_type'		=>
				[
					'width' => 100, 'align' => 'center', 'export' => false, 'col_type' => 'combo',
					'options'		=>
						[
							'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
							'array'	=> $ruleTypeOptions
						],
				],
			'shiftgroup_id'	=> 	['width' => 200, 'col_type' => 'combo', 'grid' => true, 'export' => false,
				'from_json' => true,
				'default_value'	=> '',
				'options'		=>	[
					'mode'								=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
					'modelSelectionModel'   	        => $daytypeGroup,
					'modelSelectionCriteriaGridMode'    => $daytypeGroupCriteriaGrid,
					'modelSelectionCriteriaDialogMode'  => $daytypeGroupCriteriaGrid,
					'comboId'				            => 'daytype_group_id',
					'comboValue'			            => 'daytype_group_name',
					'array'								=> [["id" => "", "value" => ""]]
				],
			],
			'workgroup_ids'	=> 	['width' => 200, 'col_type' => 'combo', 'grid' => true, 'export' => false,
				'multiple'		=> 1,
				'from_json' => true,
				'default_value' => '',
				'options'	=>	[
					'mode'								=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
					'modelSelectionModel'   	        => $workgroup,
					'modelSelectionCriteriaGridMode'    => $workgroupCriteriaGrid,
					'modelSelectionCriteriaDialogMode'  => $workgroupCriteriaGrid,
					'comboId'				            => 'workgroup_id',
					'comboValue'			            => 'workgroup_name',
				],
			],
			'minimum' 	=> ['width' => 80,	'col_type' => 'ed', 'grid' => false, 'export' => false, 'from_json' => true],
			'maximum' 	=> ['width' => 80,	'col_type' => 'ed', 'grid' => false, 'export' => false, 'from_json' => true],
			'optimum' 	=> ['width' => 80,	'col_type' => 'ed', 'grid' => false, 'export' => false, 'from_json' => true],
			'coeff' 	=> ['width' => 80,	'col_type' => 'ed', 'grid' => false, 'export' => false],
			'note'		=> ['width' => 200, 'col_type' => 'ed', 'grid' => false, 'export' => false],
		];

		$art = new ApproverRelatedGroup;
		$gargCompetencySQL = $art->getApproverReleatedGroupSQL("Competency", "companyMainData", false, "'{ $today}'", "AND", $today);

		$competency = new Competency();
		$competencyCriteriaGrid = $competency->getColumnGridCriteria($gargCompetencySQL['where']);

		$columns["twoCompetencyDoNotWorkInOneShiftGroup"] = [
			'group_value'	=> [
				'width' => 150, 'align' => 'left', 'col_type' => 'combo', 'grid' => true, 'export' => true,  'window' => true,
				'options'	=> [
					'mode'					            => Grid2Controller::G2BC_QUERY_MODE_MODEL,
					'modelSelectionModel'   	        => $company,
					'modelSelectionCriteriaGridMode'    => $companyCriteriaGrid,
					'modelSelectionCriteriaDialogMode'  => $companyCriteriaGrid,
					'comboId'				            => 'company_id',
					'comboValue'			            => 'company_name',
					'array' => ($enableCompanyAll)?[["id"=>"ALL","value"=>Dict::getValue("all")]]:"",
				],
			],
			'rule_name'		=> ['width' => 200, 'align' => 'left', 'col_type' => 'ed', 'grid' => true, 'export' => true],
			'rule_type'		=>
				[
					'width' => 100, 'align' => 'center', 'export' => false, 'col_type' => 'combo',
					'options'		=>
						[
							'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
							'array'	=> $ruleTypeOptions
						],
				],
			'shiftgroup_id'	=> 	['width' => 200, 'col_type' => 'combo', 'grid' => true, 'export' => false,
				'from_json' => true,
				'default_value'	=> '',
				'options'		=>	[
					'mode'								=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
					'modelSelectionModel'   	        => $daytypeGroup,
					'modelSelectionCriteriaGridMode'    => $daytypeGroupCriteriaGrid,
					'modelSelectionCriteriaDialogMode'  => $daytypeGroupCriteriaGrid,
					'comboId'				            => 'daytype_group_id',
					'comboValue'			            => 'daytype_group_name',
					'array'								=> [["id" => "", "value" => ""]]
				],
			],
			'competency_id_1'	=> 	['width' => 200, 'col_type' => 'combo', 'grid' => true, 'export' => false,
				'from_json' => true,
				'default_value' => '',
				'options'	=>	[
					'mode'								=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
					'modelSelectionModel'   	        => $competency,
					'modelSelectionCriteriaGridMode'    => $competencyCriteriaGrid,
					'modelSelectionCriteriaDialogMode'  => $competencyCriteriaGrid,
					'comboId'				            => 'competency_id',
					'comboValue'			            => 'competency_name',
					'array'								=> [["id" => "", "value" => ""]]
				],
			],
			'competency_id_2'	=> 	['width' => 200, 'col_type' => 'combo', 'grid' => true, 'export' => false,
				'from_json' => true,
				'default_value' => '',
				'options'	=>	[
					'mode'								=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
					'modelSelectionModel'   	        => $competency,
					'modelSelectionCriteriaGridMode'    => $competencyCriteriaGrid,
					'modelSelectionCriteriaDialogMode'  => $competencyCriteriaGrid,
					'comboId'				            => 'competency_id',
					'comboValue'			            => 'competency_name',
					'array'								=> [["id" => "", "value" => ""]]
				],
			],
			'coeff' 	=> ['width' => 80,	'col_type' => 'ed', 'grid' => false, 'export' => false, 'window' => true],
			'note'		=> ['width' => 200, 'col_type' => 'ed', 'grid' => false, 'export' => false, 'window' => true],
		];
		return $columns;
	}

	public function attributeLabels()
	{
		$attributeLabels['dhtmlxGrid'] = [
			'group_value'		=> Dict::getValue("company_id"),
			'rule_name'			=> Dict::getValue("name"),
			'rule_id'			=> Dict::getValue("SchedulingAssistantRuleIdFieldName"),
			'rule_type'			=> Dict::getValue("SchedulingAssistantRuleTypeFieldName"),
			'rule_setting'		=> Dict::getValue("SchedulingAssistantRuleSettingsFieldName"),
			'coeff'				=> Dict::getValue("SchedulingAssistantCoeffFieldName"),
			'note'				=> Dict::getValue("note"),
		];

		$attributeLabels['shiftgroupRuleWithWorkgroup'] = [
			'group_value'		=> Dict::getValue("company_id"),
			'rule_name'			=> Dict::getValue("name"),
			'rule_type'			=> Dict::getValue("SchedulingAssistantRuleTypeFieldName"),
			'shiftgroup_id'		=> Dict::getValue("daytype_group_id"),
			'workgroup_id'		=> Dict::getValue("workgroup"),
			'minimum'			=> Dict::getValue("minimum"),
			'maximum'			=> Dict::getValue("maximum"),
			'optimum'			=> Dict::getValue("optimum"),
			'coeff'				=> Dict::getValue("SchedulingAssistantCoeffFieldName"),
			'note'				=> Dict::getValue("note"),
		];

		$attributeLabels['shiftgroupRuleWithWorkgroups'] = [
			'group_value'		=> Dict::getValue("company_id"),
			'rule_name'			=> Dict::getValue("name"),
			'rule_type'			=> Dict::getValue("SchedulingAssistantRuleTypeFieldName"),
			'shiftgroup_id'		=> Dict::getValue("daytype_group_id"),
			'workgroup_ids'		=> Dict::getValue("workgroup"),
			'minimum'			=> Dict::getValue("minimum"),
			'maximum'			=> Dict::getValue("maximum"),
			'optimum'			=> Dict::getValue("optimum"),
			'coeff'				=> Dict::getValue("SchedulingAssistantCoeffFieldName"),
			'note'				=> Dict::getValue("note"),
		];

		$attributeLabels['twoCompetencyDoNotWorkInOneShiftGroup'] = [
			'group_value'		=> Dict::getValue("company_id"),
			'rule_name'			=> Dict::getValue("name"),
			'rule_type'			=> Dict::getValue("SchedulingAssistantRuleTypeFieldName"),
			'shiftgroup_id'		=> Dict::getValue("daytype_group_id"),
			'competency_id_1'	=> Dict::getValue("competency_id") . " 1",
			'competency_id_2'	=> Dict::getValue("competency_id") . " 2",
			'coeff'				=> Dict::getValue("SchedulingAssistantCoeffFieldName"),
			'note'				=> Dict::getValue("note"),
		];

		return $attributeLabels;
	}

    protected function fetchGridData($filter, $isExport = false, $excelExport = false, $csvString = false)
	{
		$gridID = requestParam('gridID');
		if ($gridID != 'dhtmlxGrid') { return []; }

		$retArr = [
			"data"	=> [],
			"log"	=> "***schedulingAssistantRuleByGroup***",
		];
		$today = date("Y-m-d");
		$art = new ApproverRelatedGroup;
		$gargCompanySQL = $art->getApproverReleatedGroupSQL("Company", "companyMainData", false, "'{$today}'", "AND", $today);
		$gargWhere = preg_replace("/AND/", "", $gargCompanySQL['where'], 1);

		$SchedulingAssistantRuleByGroup = new SchedulingAssistantRuleByGroup();
		$criteria = new CDbCriteria();
		$criteria->alias = "rbg";
		$criteria->join = "LEFT JOIN company ON company.company_id = rbg.group_value";
		$criteria->addCondition('rbg.`status` = :status');
		$criteria->params= array(':status'=> '' . $this->publishedStatus );
		$criteria->addCondition($gargWhere);
		$criteria->order = "`row_id` DESC";

		$results = $SchedulingAssistantRuleByGroup->findAll($criteria);

		// two-competency-do-not-work-in-one-shift-group: két kompetencia ne dolgozzon azonos naptípuscsoportban
		// shiftgroup-rule-with-workgroups : naptípuscsoport és munkacsoport létszám

		foreach ($results as $result) {
			$row["pk"] = $result['row_id'];
			$row["columns"]['group_value']['data'] = $result->group_value;
			$row["columns"]['rule_name']['data'] = $result->rule_name;
			$row["columns"]['rule_id']['data'] = $result->rule_id;
			$row["columns"]['rule_type']['data'] = $result->rule_type;

			$ruleSetting = json_decode($result['rule_setting']);
			switch ($result['rule_id']) {
				case 'shiftgroup-rule-with-workgroup':
					$row["columns"]['rule_setting']['data'] = $this->decodeShiftgroupRuleWithWorkgroup($ruleSetting);
					break;
				case 'shiftgroup-rule-with-workgroups':
					$row["columns"]['rule_setting']['data'] = $this->decodeShiftgroupRuleWithWorkgroups($ruleSetting);
					break;
				case 'two-competency-do-not-work-in-one-shift-group':
					$row["columns"]['rule_setting']['data'] = $this->decodeTwoCompetencyDoNotWorkInOneShiftGroupToString($ruleSetting);
					break;
				default:
					$row["columns"]['rule_setting']['data'] = $result->group_value;
			}

			$row["columns"]['coeff']['data'] = $result->coeff;
			$row["columns"]['note']['data'] = $result->note;
			$row["columns"]['workgroup_id']['data'] = $ruleSetting->workgroup_id;
			$row["columns"]['rule_by_group_id']['data'] = $result->rule_by_group_id;
			$retArr['data'][] = $row;
		}
		$retArr["dhtmlxGrid"] = true;
		return $retArr;
	}

	protected function decodeShiftgroupRuleWithWorkgroup($ruleSettingFromTable)
	{
		$dayTypeGroupName = $this->getDaytypeGroupNameArraySearch($ruleSettingFromTable->shiftgroup_id);
		$workgroupNames = $this->getWorkgroupArraySearch($ruleSettingFromTable->workgroup_id). ", ";

		$result = Dict::getValue("daytype_group") . ": " . $dayTypeGroupName . "; ";
		$result .= Dict::getValue("workgroup") . ": " . $workgroupNames ;
		if (!empty($ruleSettingFromTable->minimum))
			$result .= "Min: " . $ruleSettingFromTable->minimum . "; ";
		if (!empty($ruleSettingFromTable->maximum))
			$result .= "Max: " . $ruleSettingFromTable->maximum . "; ";
		if (!empty($ruleSettingFromTable->optimum))
			$result .= "Opt: " . $ruleSettingFromTable->optimum . "; ";
		return $result;
	}

	protected function decodeShiftgroupRuleWithWorkgroups($ruleSettingFromTable)
	{
		$dayTypeGroupName = $this->getDaytypeGroupNameArraySearch($ruleSettingFromTable->shiftgroup_id);
		$workgroupNames = "";
		foreach ($ruleSettingFromTable->workgroup_ids as $id => $workgroupId){
			$workgroupNames .= $this->getWorkgroupArraySearch($workgroupId). ", ";
		}

		$result = Dict::getValue("daytype_group") . ": " . $dayTypeGroupName . "; ";
		$result .= Dict::getValue("workgroup") . ": " . $workgroupNames ."; ";
		if (!empty($ruleSettingFromTable->minimum))
			$result .= "Min: " . $ruleSettingFromTable->minimum . "; ";
		if (!empty($ruleSettingFromTable->maximum))
			$result .= "Max: " . $ruleSettingFromTable->maximum . "; ";
		if (!empty($ruleSettingFromTable->optimum))
			$result .= "Opt: " . $ruleSettingFromTable->optimum . "; ";
		return $result;
	}

	protected function decodeTwoCompetencyDoNotWorkInOneShiftGroupToString($ruleSettingFromTable)
	{
		$dayTypeGroupName = $this->getDaytypeGroupNameArraySearch($ruleSettingFromTable->shiftgroup_id);
		$competencyName1 = $this->getCompetencyArraySearch($ruleSettingFromTable->competency_id_1);
		$competencyName2 = $this->getCompetencyArraySearch($ruleSettingFromTable->competency_id_2);

		$result = Dict::getValue("daytype_group") . ": " . $dayTypeGroupName . "; ";
		$result .= Dict::getValue("competency_id").": " . $competencyName1 . ", ";
		$result .= $competencyName2 . "; ";

		return $result;
	}

	private function getDaytypeGroupNameArraySearch($daytypeGroupId)
	{
		return $this->dayTypeGroup[$daytypeGroupId]->daytype_group_name;
	}
	private function getCompetencyArraySearch($competencyId)
	{
		return $this->competency[$competencyId]->competency_name;
	}
	private function getWorkgroupArraySearch($workgroupId)
	{
		return $this->workgroup[$workgroupId]->workgroup_name;
	}

	protected function getStatusButtons($gridID = null)
	{
		$buttons = [];
		$buttons["shiftgroupRuleWithWorkgroup"] = [
			"type"		=> "button",
			"id"		=> "shiftgroupRuleWithWorkgroup",
			"class"		=> "shiftgroupRuleWithWorkgroup",
			"name"		=> "shiftgroupRuleWithWorkgroup",
			"img"		=> "/images/status_icons/st_backup_opened.png",
			"label"		=> "shiftgroupRuleWithWorkgroup",
			"onclick"	=> "shiftgroupRuleWithWorkgroup('" . Dict::getValue("backup") . "', '" . Dict::getValue("errors") . "');",
		];

		$buttons["shiftgroupRuleWithWorkgroups"] = [
			"type"		=> "button",
			"id"		=> "shiftgroupRuleWithWorkgroups",
			"class"		=> "shiftgroupRuleWithWorkgroups",
			"name"		=> "shiftgroupRuleWithWorkgroups",
			"img"		=> "/images/status_icons/st_restore_opened.png",
			"label"		=> "shiftgroupRuleWithWorkgroups",
			"onclick"	=> "shiftgroupRuleWithWorkgroups('" . Dict::getValue("please_select_line") . "', '" . Dict::getValue("are_you_restore_the_previous_backup") . "');",
		];

		$buttons["twoCompetencyDoNotWorkInOneShiftGroup"] = [
			"type"		=> "button",
			"id"		=> "twoCompetencyDoNotWorkInOneShiftGroup",
			"class"		=> "twoCompetencyDoNotWorkInOneShiftGroup",
			"name"		=> "twoCompetencyDoNotWorkInOneShiftGroup",
			"img"		=> "/images/status_icons/st_restore_opened.png",
			"label"		=> "twoCompetencyDoNotWorkInOneShiftGroup",
			"onclick"	=> "twoCompetencyDoNotWorkInOneShiftGroup('" . Dict::getValue("please_select_line") . "', '" . Dict::getValue("are_you_restore_the_previous_backup") . "');",
		];
		$originalButtons = parent::getStatusButtons();

		return Yang::arrayMerge($buttons, $originalButtons);
	}

	public function actionDialog($layout = '//Grid2/layouts/dialogLayout', $view = '/Grid2/dialog', $additionalParams = [])
	{
		$this->G2BInit();
		$dialogMode = (int)requestParam('dialogMode'); // G2BC_DIALOG_MODE_{ADD,MOD,DET}
		$generateFrom = requestParam('generate_from');
		$editPK = requestParam('editPK');
		if ($dialogMode == 1) {
			$schedulingModify = new SchedulingAssistantRuleByGroup();
			$criteria = new CDbCriteria();
			$criteria->select = "`rule_id`, `rule_setting`";
			$criteria->addCondition('`row_id` = :rowID');
			$criteria->params = array(':rowID'=> ''.$editPK);
			$schedulingModifyResult = $schedulingModify->find($criteria);
			$ruleSetting = json_decode($schedulingModifyResult->rule_setting);

			$_POST['actColumnValue']['group_value']		= $ruleSetting->group_value;
			$_POST['actColumnValue']['shiftgroup_id'] 	= $ruleSetting->shiftgroup_id;

			switch ($schedulingModifyResult->rule_id)
			{
				case 'shiftgroup-rule-with-workgroup':
					$_POST['generate_from'] = 'shiftgroupRuleWithWorkgroup';
					$_POST['actionSaveUrl'] = 'sheduleAssistanceRuleByGroupUpdate';
					$_POST['actColumnValue']['workgroup_id']	= $ruleSetting->workgroup_id;
					$_POST['actColumnValue']['minimum'] = $ruleSetting->minimum;
					$_POST['actColumnValue']['maximum'] = $ruleSetting->maximum;
					$_POST['actColumnValue']['optimum'] = $ruleSetting->optimum;
					break;
				case 'shiftgroup-rule-with-workgroups':
					$_POST['generate_from'] = 'shiftgroupRuleWithWorkgroups';
					$_POST['actionSaveUrl'] = 'sheduleAssistanceRuleByGroupUpdate';
					$_POST['actColumnValue']['workgroup_ids'] = $ruleSetting->workgroup_ids;
					$_POST['actColumnValue']['minimum'] = $ruleSetting->minimum;
					$_POST['actColumnValue']['maximum'] = $ruleSetting->maximum;
					$_POST['actColumnValue']['optimum'] = $ruleSetting->optimum;
					break;
				case 'two-competency-do-not-work-in-one-shift-group':
					$_POST['generate_from'] = 'twoCompetencyDoNotWorkInOneShiftGroup';
					$_POST['actionSaveUrl'] = 'sheduleAssistanceRuleByGroupUpdate';
					$_POST['actColumnValue']['competency_id_1']	= $ruleSetting->competency_id_1;
					$_POST['actColumnValue']['competency_id_2']	= $ruleSetting->competency_id_2;
					break;
				default:
					;
			}
		}
		parent::actionDialog('//Grid2/layouts/dialogLayout', '/Grid2/dialog', []);
	}

	public function actionDropdown()
	{
		$generateFromElementid	= requestParam('generate_from') . '.' . requestParam('id');
		$actColumnValue = requestParam('actColumnValue');
		switch ($generateFromElementid)
		{
			case 'shiftgroupRuleWithWorkgroup.3':
			case 'shiftgroupRuleWithWorkgroups.3':
			case 'twoCompetencyDoNotWorkInOneShiftGroup.3':

				$_POST['default_value'] = $actColumnValue['shiftgroup_id'];
				break;
			case 'shiftgroupRuleWithWorkgroup.4':
				$_POST['default_value'] = $actColumnValue['workgroup_id'];
				break;
			case 'shiftgroupRuleWithWorkgroups.4':
				$_POST['default_value'] = $actColumnValue['workgroup_ids'];
				break;
			case 'twoCompetencyDoNotWorkInOneShiftGroup.4':
				$_POST['default_value'] = $actColumnValue['competency_id_1'];
				break;
			case 'twoCompetencyDoNotWorkInOneShiftGroup.5':
				$_POST['default_value'] = $actColumnValue['competency_id_2'];
				break;
		}
		parent::actionDropdown();
	}

	public function filters()
	{
		return [
			'accessControl', // perform access control for CRUD operations
		];
	}

	public function accessRules()
	{
		return [
			['allow', // allow authenticated users to access all actions
				'users' => ['@'],
			],
			['deny',  // deny all users
				'users' => ['*'],
			],
		];
	}

	public function actionsheduleAssistanceRuleByGroupSave()
	{
		$generateFrom = requestParam('generate_from');
		$params = requestParam("dialogInput_{$generateFrom}");
		$newRow = new SchedulingAssistantRuleByGroup();
		$this->saveRoleByGroup($newRow, $params, 'save');
	}

	public function actionsheduleAssistanceRuleByGroupUpdate()
	{
		$generateFrom = requestParam('generate_from');
		$params = requestParam("dialogInput_{$generateFrom}");
		$rowId = $params['row_id'];
		$upd = SchedulingAssistantRuleByGroup::model()->findByPk($rowId);
		$this->saveRoleByGroup($upd, $params, 'update');
	}

	protected function saveRoleByGroup($model, $params, $type='save')
	{
		$status = 1;
		$title = '';
		$error = '';

		$model->attributes = $this->getSchedulingAssistantRuleByGroupAttributes($model, $params);

		if ($this->checkAttributesChanges($model)) {

			if ($model->validate()) {
				if ($type === 'update')	$model->update();
				if ($type === 'save')	$model->save();
			} else {
				$status = 0;
				$title = Dict::getValue("error");
				$error = "<div style=\"text-align: center; font-weight: bold; font-size: large;\">" . Dict::getValue("error") . "</div>" .
					getErrorsFromModel($model);
			}
		}

		$status = [
			'status' => $status,
			'title' => $title,
			'error'	=> $error
		];
		echo json_encode($status);

	}

	protected function checkAttributesChanges($model)
	{
		$oldAttributes = $model->getOldAttributes();
		$oldRuleSettings = json_decode($oldAttributes['rule_setting']);
		unset($oldAttributes['rule_setting']);
		$attributes = $model->attributes;
		$ruleSettings = json_decode($attributes['rule_setting']);
		unset($attributes['rule_setting']);
		//$result1 = array_diff_assoc($oldAttributes, $attributes);
		//$result2 = array_diff_assoc($oldRuleSettings, $ruleSettings);
		if ($attributes == $oldAttributes && $oldRuleSettings == $ruleSettings) {
			return false;
		}
		return true;
	}

	protected function getSchedulingAssistantRuleByGroupAttributes($model, $params)
	{
		$generateFrom = requestParam('generate_from');
		return [
			'rule_id'			=> $this->getRuleIdByGeneratedFrom($generateFrom),
			'group_id'			=> 'company_id',
			'group_value'		=> $params['group_value'],
			'rule_name'			=> $params['rule_name'],
			'rule_type'			=> $params['rule_type'],
			'rule_setting'		=> $this->getRuleSetting($model, $generateFrom, $params),
			'coeff'				=> $params['coeff'],
			'note'				=> $params['note'],
			'status' 			=> Status::PUBLISHED,
		];
	}

	protected function getRuleIdByGeneratedFrom($generatedFrom)
	{
		return $this->ruleIByGeneratedFrom[$generatedFrom];
	}

	protected function getRuleSetting($model, $generateFrom, $params)
	{
		$columns = $this->columns();
		$result = [];
		foreach ($columns[$generateFrom] as $columnName => $attr) {
			if (!isset($attr['from_json']))	continue;
			if (empty($params[$columnName])) continue;
			$parameters = $model->getJsonColumnParameters($columnName);
			if ($parameters['type'] === 'integer') {
				$params[$columnName] = (integer)$params[$columnName];
			}
			$result[$columnName] = $params[$columnName];
		}
		return json_encode($result);
	}
}
