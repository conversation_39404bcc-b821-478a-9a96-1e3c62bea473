<?php
/**
 * Dolgozói juttatási egyenleg feltöltése xlsx-ből
 * A dialogban kiválasztott fájlt már kiválasztáskor feltölti a /fileupload/fileUpload/upload, majd a mentéskor feldolgozza az ebből kiolvasott adatokat
 */
class EmployeeBenefitBalanceUploadController extends Grid2Controller
{
	const DEBUG = \TRUE;
	
	private $columns = ['errors', 'fullname', 'emp_id', 'employee_id', 'benefit_balance', 'valid_from', 'valid_to'];
	
	private int $errorRowNum = 0;
	private int $nonInserted = 0;
	
	private string $errorFields = '';
	private string $publishedStatus;
	private string $deletedStatus;
	private string $defaultEnd;
	private int $maxBenefitBalance;
	private string $employeeBenefitBalanceModelAndColumn;
	
	public function __construct()
	{
		parent::__construct("uploaders/employeeBenefitBalanceUpload");
		Yang::registerScriptFile(baseURL() . '/js/uploaders.js');

		$this->publishedStatus  						= Status::PUBLISHED;
		$this->deletedStatus    						= Status::STATUS_DELETED;
		$this->maxBenefitBalance						= App::getSetting("maxEmployeeBenefitBalance");
		$this->employeeBenefitBalanceModelAndColumn		= App::getSetting("employeeBenefitBalanceModelAndColumn");
		$this->defaultEnd								= App::getSetting("defaultEnd");
		
		parent::enableLAGrid();
	}
	
	protected function G2BInit()
    {
		parent::G2BInit();
		parent::enableMultiGridMode();
		parent::setControllerPageTitleId("page_title_employee_benefit_balance_upload");

		$this->LAGridRights->overrideInitRights("paging",				true, 'dhtmlxGrid');
		$this->LAGridRights->overrideInitRights("search",				false, 'dhtmlxGrid');
		$this->LAGridRights->overrideInitRights("search_header",		true, 'dhtmlxGrid');
		$this->LAGridRights->overrideInitRights("select",				false, 'dhtmlxGrid');
		$this->LAGridRights->overrideInitRights("multi_select",			false, 'dhtmlxGrid');
		$this->LAGridRights->overrideInitRights("reload_sortings",		true, 'dhtmlxGrid');
		$this->LAGridRights->overrideInitRights("details",				false, 'dhtmlxGrid');
		$this->LAGridRights->overrideInitRights("init_open_search",		false, 'dhtmlxGrid');
		$this->LAGridRights->overrideInitRights("export_xlsx",			true, 'dhtmlxGrid');
		$this->LAGridRights->overrideInitRights("reload",				true, 'dhtmlxGrid');

		$this->LAGridDB->enableSQLMode("dhtmlxGrid");
		
		$SQL = "SHOW TABLES LIKE 'temp_employee_benefit_balance_upload';";
		$result = dbFetchValue($SQL) ? true : false;
		
		$SQL = "SELECT NULL";
		if ($result) {
			$SQL="
				SELECT
					*
				FROM
					`temp_employee_benefit_balance_upload`
				ORDER BY 
					`errors` = '', `row_id`+0 ASC
				";
		}
		$this->LAGridDB->setSQLSelection($SQL, "row_id", "dhtmlxGrid");
    }
	
	protected function fetchGridData($filter, $isExport = false, $excelExport = false, $csvString = false) 
	{
		WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__);
		if (!isset($filter)) { $filter = null; }
		$gridID = requestParam('gridID');

		$retArr = parent::fetchGridData($filter);
		
		if ($gridID === "dhtmlxGrid") {
			for ($i = 0; $i < count($retArr['data']); $i++){
				$errorCols = explode(';', $retArr['data'][$i]['columns']['errors']['data']);
				if(!empty($errorCols[0])){
					$retArr['data'][$i]['cssClass'] .= ' bluegrey';
				}
				foreach ($retArr['data'][$i]['columns'] as $key => &$value){
					if(in_array($key, $errorCols)){
						$value['cssClass'] = 'red';
					}
				}
			}
			
			return $retArr;
		} else {
			return [];
		}
	}
	
	/**
	 * Az alap gombokhoz hozzáadjuk még a feltöltő gombot
	 * @param string $gridID
	 * @return array
	 */
	protected function getStatusButtons($gridID = null) 
    {
		$buttons["openImportDialog"] = array(
								"type" => "button",
								"id" => "openImportDialog",
								"class" => "openImportDialog",
								"name" => "openImportDialog",
								"img" => "/images/status_icons/st_upload.png",
								"label" => Dict::getValue("import")/*."(Ctrl+Shift+a)"*/,
								"onclick" => "G2BDialogMultiGrid('uploadDialog','uploadDialog',null,0,'./dialog','./processFile','./gridData',null,'".$this->getControllerPageTitle()." - ".Dict::getValue("import")."','');",
							);
		$buttons[] = ["type" => "selector",];
		
		$buttons["save"] = array(
								"type" => "button",
								"id" => "save",
								"class" => "save",
								"name" => "save",
								"img" => "/images/status_icons/st_save.png",
								"label" => Dict::getValue("save"),
								"onclick"	=> "saveDataUploadDialog('" . Dict::getValue("are_you_sure_save_all") . "', 
																		'saveEmployeeBenefitBalance', 
																		'" . Dict::getValue("uploader_fail_500") . "');",
							);
		$buttons[] = ["type" => "selector"];
		
		$buttons = array_merge($buttons, parent::getStatusButtons($gridID));

		return $buttons;
	}
	
	/**
	 * A /fileupload/fileUpload/upload-ban feltöltött fájl feldolgozása, kötelező a fejléc!!!
	 */
	public function actionProcessFile()
	{
		$file = $_SESSION['file']['path'] . $_SESSION['file']['name'];

		$data = XlsxToArray::getSpreadsheetExcelFileToArray($file);

		$dropTempTableSql = "DROP TABLE IF EXISTS `temp_employee_benefit_balance_upload`;";
		dbExecute($dropTempTableSql);

		$createTempTableSql = "
				CREATE TABLE `temp_employee_benefit_balance_upload`(
					`row_id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
					";

		foreach ($this->columns as $col) {
			$createTempTableSql .= "`$col` ".($col == 'errors' ? "LONGTEXT" : "TEXT").",
					";
		}

		$createTempTableSql .= "
					PRIMARY KEY (`row_id`)
				) ENGINE = InnoDB DEFAULT CHARACTER SET = utf8;";

		dbExecute($createTempTableSql);
		
		$status = '';
		$columns = implode("`, `", $this->columns);
		unset($data[1]);

		foreach ($data as $key => $value) 
        {
			$yearMonth 	= trim($value['D'] ?? '');
			$empId 		= trim($value['B'] ?? '');

			$row = [
				'fullname' 			=> trim($value['A']),
				'emp_id' 	        => $empId,
				'employee_id'		=> !empty($empId) ? Employee::getEmployeeIdByEmpId($empId) : '',
				'benefit_balance' 	=> trim($value['C'] ?? ''),
                'valid_from'        => !empty($yearMonth) ? date("Y-m-d") : '',
                'valid_to'          => !empty($yearMonth) ? $this->defaultEnd : ''
			];
		
			$values = implode("', '", array_map(function ($value) {
				return str_replace("'", "\'", is_null($value) ? null : $value);
			}, array_values($row)));

			$errors = $this->validateDataRow($row) . $this->errorFields;
			
			$SQL = "INSERT INTO `temp_employee_benefit_balance_upload` (`$columns`) VALUES ('$errors','$values');";
			
			$insertedRow = dbExecute($SQL);
		}
		
		echo json_encode(['success_status' => 1, 'title' => Dict::getValue('message'), 'message' => Dict::getValue('data_upload_finished')]);
	}
	
	public function columns()
	{
		$columns['dhtmlxGrid']['errors']			= ['export'=> false, 'col_type'=>'ed', 'width' => 'null'];
		$columns['dhtmlxGrid']['fullname']			= ['export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200];
		$columns['dhtmlxGrid']['emp_id']			= ['grid'=>true, 'width'=>150, 'window'=>true, 'export'=> true, 'col_type'=>'ed'];
		$columns['dhtmlxGrid']['employee_id']		= ['grid'=>false, 'width'=>150, 'window'=>true, 'export'=> false, 'col_type'=>'ed'];
		$columns['dhtmlxGrid']['benefit_balance']	= ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed'];
		$columns['dhtmlxGrid']['valid_from']		= ['grid'=>true, 'width'=>250, 'window'=>true, 'export'=> true, 'col_type'=>'ed'];
		$columns['dhtmlxGrid']['valid_to']			= ['grid'=>true, 'width'=>250, 'window'=>true, 'export'=> true, 'col_type'=>'ed'];


		$columns['uploadDialog']['xlsx_file']		= ['export'=> false, 'report_width' => 20, 'col_type'=>'fileUpload', 'dialog_width'=>'2'];

		return $columns;
	}
	
	public function attributeLabels() 
	{
		$attributeLabels['dhtmlxGrid'] =  
		[
			'errors'			=> Dict::getValue('errors'),
			'fullname'			=> Dict::getValue('fullname'),
			'emp_id'			=> Dict::getValue('emp_id'),
			'benefit_balance'	=> Dict::getValue('benefit_balance'),
			'valid_from'		=> Dict::getValue('valid_from'),
			'valid_to'			=> Dict::getValue('valid_to')
		];

		return $attributeLabels;
	}
	
	/**
	 * Leellenőrzi, hogy a kötelező adatok ki lettek-e töltve, illetve helyesek-e az adatok
	 * @param array $data
	 */
	private function validateDataRow($data) 
	{
		$fields = ['emp_id', 'benefit_balance', 'valid_from', 'valid_to'];
		$colErrors = '';

		foreach ($fields as $field) 
		{
			if (empty($data[$field])) {
				$colErrors .= $field . ';';
			}
		}

		if (empty($data['employee_id'])) {
			$colErrors .= 'emp_id;';
		}

		if ((int) $data['benefit_balance'] > $this->maxBenefitBalance || (int) $data['benefit_balance'] < 0) {
			$colErrors .= 'benefit_balance;';
		}

		return $colErrors;
	}
	
	public function actionSaveEmployeeBenefitBalance() 
	{	
		$uploadedTempSQL = "
			SELECT 
				*
			FROM
				`temp_employee_benefit_balance_upload`
			WHERE
				`errors` IS NULL OR `errors` = ''";

		$savedData = dbFetchAll($uploadedTempSQL);
		
		$modelAndColumnToSave = explode(";",$this->employeeBenefitBalanceModelAndColumn);
		$balanceModel 	= $modelAndColumnToSave[0];
		$balanceColumn 	= $modelAndColumnToSave[1];

		$columnsToSave = ['employee_id', $balanceColumn, 'valid_from', 'valid_to'];

		foreach ($savedData as $savedDataRow) 
		{
			$balance = $savedDataRow['benefit_balance'];
			$savedDataRow[$balanceColumn] = $balance;

			$this->insertEmployeeBenefitBalanceData($balanceModel, $savedDataRow, $columnsToSave, $balanceColumn);
		}
		
		$status = ['status' => 1, 'title' => Dict::getValue('message'), 'message' => Dict::getValue('data_upload_finished')];
		
		echo json_encode($status);
	}

	private function insertEmployeeBenefitBalanceData($balanceModel, $allData, $columns, $balanceColumn)
	{
		$balance = $balanceModel::model()->findByAttributes([
			'employee_id'	=> $allData['employee_id'],
			'status'		=> $this->publishedStatus,
			'valid_from'	=> date("Y-m-d"),
			'valid_to'		=> $this->defaultEnd
		]);

		if (!$balance) {
			$balance	= new $balanceModel;

			foreach ($columns as $column) {
				$balance->{$column} = $allData[$column];
			}
		} else {
			$balance->{$balanceColumn} = $allData[$balanceColumn];
		}

		try {
			$balance->save();
			return true;
		} catch (\Exception $e) {
			Yii::log($e, 'log', 'employeeBenefitBalanceSaveError');
			return false;
		}
	}
}
?>