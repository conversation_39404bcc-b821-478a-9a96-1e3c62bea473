<?php
class LinkGroupToTerminalController extends Grid2Controller
{
	public const SEPARATOR = '€';
    private $related_group_id;
	private $currentGroupTable;
	public $published = Status::PUBLISHED;
	public $defaultEnd;
	private $customerDbPatchName;

	public function __construct()
	{
		parent::__construct("wfm/linkGroupToTerminal");
		$this->related_group_id = App::getSetting("link_group_to_terminal_related_id");
		if ($this->related_group_id == 'NONE' || is_null($this->related_group_id)) {
			Yang::log(
				"The value of related_group_id cannot be 'NONE' or null", 
				"error"
			);
		}
		$this->currentGroupTable = $this->getCurrentGroupTableData();
		$this->defaultEnd = App::getSetting("defaultEnd");
		$this->customerDbPatchName = Yang::customerDbPatchName();
		parent::enableLAGrid();
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("LinkGroupToTerminal");

		parent::enableMultiGridMode();
		parent::setControllerPageTitleId("page_title_link_group_to_terminal");

		$this->LAGridRights->overrideInitRights("paging", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search_header", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("select", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("multi_select", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("column_move", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload_sortings", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("details", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("add", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("modify", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("delete", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("export_xls", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("export_xlsx", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("save_state", true, "dhtmlxGrid");

		$this->LAGridDB->enableSQLMode();

		$SQL = "
		SELECT 
		* 
		FROM (
			SELECT 
				CONCAT_WS('" . self::SEPARATOR . "', '" . $this->currentGroupTable['related_id'] . "'," . $this->currentGroupTable['id_in_related_table'] . ", t.terminal_id, t.reader_id) as row_id,
				".$this->currentGroupTable['id_in_related_table']." as related_value,
				" . $this->currentGroupTable['group_name_column_in_related_table'] . " as existing_group_names,
				t.terminal_id AS terminalId,
				t.terminal_name as existing_terminal_names,
				IF(lgtt.row_id IS NULL, 0, 1) as status_active,
				lgtt.row_id as link_id,
				t.reader_id
			FROM " . $this->currentGroupTable['related_table'] . " group_table
			CROSS JOIN terminal t
			LEFT JOIN link_group_to_terminal lgtt
				ON lgtt.related_value = group_table.".$this->currentGroupTable['id_in_related_table']."
				AND lgtt.terminal_id = t.terminal_id
				AND IF(t.reader_id IS NULL, 1 , lgtt.reader_id = t.reader_id)
				AND lgtt.related_id = '".$this->currentGroupTable['related_id']."'
				AND lgtt.status = '$this->published'
			WHERE t.status = '$this->published'
			AND group_table.status = '$this->published'
			AND CURDATE() BETWEEN group_table.valid_from AND IFNULL(group_table.valid_to, '2038-01-01')
			AND CURDATE() BETWEEN t.valid_from AND IFNULL(t.valid_to, '2038-01-01')
		) as sel
		GROUP BY row_id
		ORDER BY existing_group_names, existing_terminal_names
		";
//print $SQL;exit;
		$this->LAGridDB->setSQLSelection($SQL, "row_id", "dhtmlxGrid");
		parent::setExportFileName(Dict::getValue("page_title_link_group_to_terminal"));
		parent::G2BInit();
	}

	public function search()
	{
		return array();
	}

	public function columns()
	{
		$columns = [];

		$copyFieldsSql = "	SELECT 
								CONCAT_WS('" . self::SEPARATOR . "','" . $this->currentGroupTable['id_in_related_table'] . "', ".$this->currentGroupTable['id_in_related_table'] .") AS id, 
								`" . $this->currentGroupTable['group_name_column_in_related_table'] . "` AS value
							FROM `" . $this->currentGroupTable['related_table'] . "` group_table
							WHERE group_table.status = '" . $this->published . "' 
							AND CURDATE() BETWEEN group_table.valid_from AND IFNULL(group_table.valid_to, '" . $this->defaultEnd . "') ";

		$toCondition = " AND `" . $this->currentGroupTable['id_in_related_table'] . "` <> '{from_group}'";
		$copyFieldsOrder = " ORDER BY `" . $this->currentGroupTable['group_name_column_in_related_table'] . "` ASC";

		$columns['copyDialog'] =
			[
				'from_group' => [
					'grid'       => false,
					'export'     => true,
					'col_type'   => 'combo',
					'label_text' => Dict::getValue("from_group"),
					'onchange'   => ['to_group'],
					'options'    => [
						'mode'  => Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'   => $copyFieldsSql . $copyFieldsOrder,
						'array' => [["id" => "", "value" => ""]],
					],
				],
				'to_group'   => [
					'grid'       => false,
					'export'     => true,
					'col_type'   => 'combo',
					'label_text' => Dict::getValue("to_group"),
					'options'    => [
						'mode'  => Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'   => $copyFieldsSql . $toCondition . $copyFieldsOrder,
						'array' => [["id" => "", "value" => ""]],
					],
				],
				'overwrite'  => [
					'id'            => "overwrite",
					'default_value' => "no",
					'col_type'      => 'combo',
					'label_text'    => Dict::getValue("overwrite"),
					'options'       => [
						'mode'  => Grid2Controller::G2BC_QUERY_MODE_ARRAY,
						'array' => App::getLookup('yes_no')
					],
				]
			];


		$columns['dhtmlxGrid'] = array(
			'status_active'           => [
				'grid'     => true,
				'window'   => false,
				'export'   => true,
				'col_type' => 'cb',
				'width'    => 20
			],
			'related_value'           => array(
				'export'   => false,
				'grid'     => false,
				'window'   => true,
				'col_type' => 'combo',
				'align'    => 'left',
				'options'  => array(
					'mode'  => Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'   => "SELECT	
									group_table." . $this->currentGroupTable['id_in_related_table'] . " as id,
									" . $this->currentGroupTable['group_name_column_in_related_table'] . " as value
								FROM " . $this->currentGroupTable['related_table'] . " group_table
								LEFT JOIN `link_group_to_terminal_group_id_table_relations` lgttgitr ON
										group_table." . $this->currentGroupTable['id_in_related_table'] . " = lgttgitr.`id_in_related_table`
									AND lgttgitr.`status` = " . $this->published . "
								WHERE
										group_table.`status` = " . $this->published . "
									AND CURDATE() BETWEEN group_table.`valid_from` AND IFNULL(group_table.`valid_to`,'" . $this->defaultEnd . "')
								GROUP BY id
								ORDER BY value",
					'array' => array(array("id" => "", "value" => Dict::getValue("choose_option"))),
				)
			),
			'existing_group_names'    => array(
				'export'       => true,
				'report_width' => 20,
				'col_type'     => 'ed',
				'width'        => 220,
				'align'        => 'left',
				'window'       => false
			),
		);

		if ($this->customerDbPatchName === "flex") {
			$columns['dhtmlxGrid'] = Yang::arrayMerge($columns['dhtmlxGrid'],
			[
				'terminalId'    => array(
					'export'       => true,
					'report_width' => 20,
					'col_type'     => 'ed',
					'width'        => 100,
					'align'        => 'left',
					'window'       => false
				),
			]);
		}

		$columns['dhtmlxGrid'] = Yang::arrayMerge($columns['dhtmlxGrid'],
		[
			'terminal_id'             => array(
				'export'   => false,
				'grid'     => false,
				'window'   => true,
				'col_type' => 'combo',
				'align'    => 'left',
				'options'  => array(
					'mode'  => Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'   => "SELECT
									t.terminal_id as id,
									t.terminal_name as value
								FROM `terminal` t
								WHERE
										t.`status` = " . $this->published . "
									AND CURDATE() BETWEEN t.`valid_from` AND IFNULL(t.`valid_to`,'" . $this->defaultEnd . "')
								GROUP BY id
								ORDER BY value",
					'array' => array(array("id" => "", "value" => Dict::getValue("choose_option"))),
				)
			),
			'existing_terminal_names' => array(
				'export'       => true,
				'report_width' => 20,
				'col_type'     => 'ed',
				'width'        => 220,
				'align'        => 'left',
				'window'       => false
			),
			'reader_id'               => array(
				'export'       => true,
				'report_width' => 20,
				'col_type'     => 'ed',
				'width'        => 220,
				'align'        => 'left',
				'window'       => false,
				'grid'         => true
			)
		]);

		return $columns;
	}

	public function attributeLabels()
	{
		$attributeLabels = [];
		$attributeLabels['dhtmlxGrid'] = array(
			'related_value'           	=> Dict::getValue($this->currentGroupTable['group_name_column_in_related_table']),
			'terminalId'				=> Dict::getValue("terminal_id"),
			'existing_group_names'    	=> Dict::getValue($this->currentGroupTable['group_name_column_in_related_table']),
			'terminal_id'             	=> Dict::getValue("terminal"),
			'existing_terminal_names' 	=> Dict::getValue("terminal"),
			'reader_id'               	=> Dict::getValue("reader_id"),
			'status_active'           	=> Dict::getValue("status_active")
		);

		return $attributeLabels;
	}

	public function actionIndex($layout = '//Grid2/layouts/indexLayout', $view = '/Grid2/index', $params = array())
	{
		parent::actionIndex($layout, $view, $params);
	}

	protected function getStatusButtons($gridID = null)
	{
		$buttons = [];

		if ($gridID === "dhtmlxGrid") {
			$buttons["openCopyDialog"] = array(
				"type"    => "button",
				"id"      => "openCopyDialog",
				"class"   => "openCopyDialog",
				"name"    => "openCopyDialog",
				"img"     => "/images/status_icons/st_copy.png",
				"label"   => Dict::getValue("copy_rights"),
				"onclick" => " G2BDialogMultiGrid('dhtmlxGrid','copyDialog', null, 0,'./dialog',
													'" . baseURL() . "/" . $this->getControllerID() . "/copyRights','./gridData',null,
													'" . Dict::getValue("copy_rights") . "',null);",
			);
			$buttons[] = [
				"type" => "selector",
			];
		}
		return Yang::arrayMerge($buttons, parent::getStatusButtons($gridID));
	}


	public function actionSaveAll()
	{
		$this->layout = "//layouts/ajax";

		$this->G2BInit();

		// GET CHECKED KEYS
		$ids = requestParam('ids');
		$idsArray = explode(";", $ids);
		$idsArray = explodeKeyVal('[]', $idsArray);

		// GET UNCHECKED KEYS
		$uncheckedIds = requestParam('unids');
		$unIdsArray = explode(";", $uncheckedIds);
		$unIdsArray = explodeKeyVal('[]', $unIdsArray);

		/*
		 * Kitörli azokat, amik nincsenek bepipálva
		 */
		$delSQL = '';
		foreach ($unIdsArray['status_active'] as $value) {
			$rowIds = explode(self::SEPARATOR, $value);
			$relatedValue = $rowIds[1];
			$relatedId = $rowIds[0];
			$terminalId = $rowIds[2];
			$readerId = !empty($rowIds[3]) ? '"'.$rowIds[3].'"' : 'NULL';

			$delSQL .= "DELETE FROM `link_group_to_terminal` 
						WHERE 
						  `related_value` = '" . $relatedValue . "' 
						  AND `terminal_id` = '" . $terminalId . "' 
						  AND related_id = '" . $relatedId . "'";
			
			if ($readerId !== 'NULL')
			{
				$delSQL .= "  AND reader_id = $readerId; ";
			}
			else
			{
				$delSQL .= "; ";
			}
		}
		if (!empty($delSQL)) {
			dbExecute($delSQL);
		}

		/*
		 * Menti azokat, amik be vannak pipálva
		 */
		foreach ($idsArray['status_active'] as $ida) {
			$explodeKey = explode(self::SEPARATOR, $ida);
			$relatedId = $explodeKey[0];
			$relatedValue = $explodeKey[1];
			$terminalId = $explodeKey[2];
			$readerId = !is_null($explodeKey[3]) ? $explodeKey[3] : NULL;

			$dtvg = new LinkGroupToTerminal();
			$crit = new CDbCriteria();
			$crit->condition = "
			`related_id` = '" . $relatedId . "' 
			AND `related_value` = '" . $relatedValue . "' 
			AND terminal_id = '" . $terminalId . "' 
			AND reader_id = '".$readerId."' 
			";
			$dvGroup = $dtvg->findAll($crit);

			if (!$dvGroup) {
				$linkGroupToTerminal = new LinkGroupToTerminal;
				$linkGroupToTerminal->related_id = $relatedId;
				$linkGroupToTerminal->related_value = $relatedValue;
				$linkGroupToTerminal->terminal_id = $terminalId;
				$linkGroupToTerminal->reader_id = is_null($readerId) ? null : $readerId;
				$linkGroupToTerminal->created_by = userID();
				$linkGroupToTerminal->created_on = date('Y-m-d H:i:s');
				$linkGroupToTerminal->save();
			}
		}

		$status = [
			'status' => 1,
		];

		echo json_encode($status, JSON_UNESCAPED_UNICODE);
	}

	private function getCurrentGroupTableData()
	{
		$SQL = "
			SELECT
				`related_table`,
				`id_in_related_table`,
				`group_name_column_in_related_table`,
			    related_id
			FROM
				`link_group_to_terminal_group_id_table_relations` lgttgitr
			WHERE
					lgttgitr.`related_id` = '" . $this->related_group_id . "'
				AND lgttgitr.`status` = " . $this->published . "
			";

		$results = dbFetchAll($SQL);

		if ($results > 0) {
			return $results[0];
		} else {
			return null;
		}
	}

	function actionCopyRights()
	{
		$input = requestParam('dialogInput_copyDialog');
		$result = ['status' => 1, 'error' => ''];

		if (empty($input['from_group']) || empty($input['to_group'])) {
			$result['status'] = 0;
			$result['error'] = Dict::getValue("empty_fields");
		} else {
			/*
			 * ha a felülírás igen értéket kap, akkor törli a meglévő beállításait az új csoportnak
			 */
			$fromGroupExplode = explode(self::SEPARATOR,$input['from_group']);
			$toGroupExplode = explode(self::SEPARATOR,$input['to_group']);

			$fromGroupRelatedId = $fromGroupExplode[0];
			$fromGroupRelatedValue = $fromGroupExplode[1];

			$toGroupRelatedId = $toGroupExplode[0];
			$toGroupRelatedValue = $toGroupExplode[1];

			if ((int)$input['overwrite']) {
				$delSQL = "
				DELETE FROM `link_group_to_terminal` 
				WHERE `related_id` = '" . $toGroupRelatedId . "' 
				AND related_value = '".$toGroupRelatedValue."'
				";
				dbExecute($delSQL);
			}

			$SQL = "
			SELECT 
			terminal_id,
			reader_id
			FROM link_group_to_terminal
			WHERE related_id = '".$fromGroupRelatedId."'
			AND related_value = '".$fromGroupRelatedValue."'
			AND status = '".$this->published."'
			";

			$groups = dbFetchAll($SQL);

			foreach ($groups as $groupKey => $groupVal){
				$linkGroupToTerminal = new LinkGroupToTerminal();
				$linkGroupToTerminal->related_id = $toGroupRelatedId;
				$linkGroupToTerminal->related_value = $toGroupRelatedValue;
				$linkGroupToTerminal->terminal_id = $groupVal['terminal_id'];
				$linkGroupToTerminal->reader_id = $groupVal['reader_id'];
				$linkGroupToTerminal->save();
			}

		}
		echo json_encode($result);
	}


}
