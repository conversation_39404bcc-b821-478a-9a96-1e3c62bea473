<?php #yii2: done

'yii2-only`;

	namespace app\controllers\pcs;
	use app\components\App;
	use app\components\Dict;
	use app\components\Grid2\Grid2Controller;
	use app\models\AuthRolegroup;
	use app\models\Status;
	use Yang;

`/yii2-only';


class AcAccessLevelRightController extends Grid2Controller
{
	public function __construct()
	{
		parent::__construct("pcs/acAccessLevelRight");

		parent::enableLAGrid();
	}

	protected function G2BInit()
	{
		parent::enableMultiGridMode();
		
		$this->LAGridDB->setModelName("AcAccessLevelRight",	"dhtmlxGrid");

		parent::setControllerPageTitleId("page_title_ac_access_level_right");

		$this->LAGridRights->overrideInitRights("paging",			true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search",			false,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search_header",	true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("select",			true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("multi_select",		true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("column_move",		true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload_sortings",	true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("details",			false,	"dhtmlxGrid");

		$this->LAGridRights->overrideInitRights("add",				true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("modify",			true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("delete",			true,	"dhtmlxGrid");

		$this->LAGridRights->overrideInitRights("reload",			true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("export_xls",		false,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("export_xlsx",		true,	"dhtmlxGrid");

		$this->LAGridDB->enableSQLMode();

		$SQL = "
			SELECT
				aalr.`row_id` AS `row_id`,
				CASE aalr.`right_type`
					WHEN 'EMPLOYEE' THEN '".Dict::getValue("employee")."'
					ELSE /*VISITOR*/ '".Dict::getValue("visitor")."'
				END AS `right_type_value`,
				arg.`rolegroup_name` AS `rolegroup_name`,
				aal.`ac_access_level_name`
			FROM
				`ac_access_level_right` aalr
			LEFT JOIN
				`auth_rolegroup` arg ON
					arg.`rolegroup_id` = aalr.`rolegroup_id`
			LEFT JOIN
				`ac_access_level` aal ON
					aal.`ac_access_level_id` = aalr.`ac_access_level_id`
					AND aal.`status` = '".Status::PUBLISHED."'
			WHERE
				aalr.`status` = '".Status::PUBLISHED."'
			ORDER BY
				arg.`rolegroup_name`, aal.`ac_access_level_name`
		";

		$this->LAGridDB->setSQLSelection($SQL,"row_id","dhtmlxGrid");

		parent::G2BInit();
	}

	public function columns()
	{
		$rolegroup = new AuthRolegroup;
		$rolegroupCriteria = new CDbCriteria();
		$rolegroupCriteria->order = "rolegroup_name ASC";
		$rolegroupCriteria->condition = "(`visibility` = 1";
		if (App::isRootSessionEnabled()) {
			$rolegroupCriteria->condition .= "
				 OR rolegroup_id='a5b6bd79e008725744118c7c46e10cda'";
		}
		$rolegroupCriteria->condition .= ")";
		
		$toRolegroupCriteria=$rolegroupCriteria;
		$toRolegroupCriteria->condition .="
				AND rolegroup_id!='{from_rolegroup}'";
		
		$columns=array();

		$role_SQL = "
			SELECT `ac_access_level_id` as id, `ac_access_level_name` AS value
			FROM `ac_access_level`
			WHERE `status` = '".Status::PUBLISHED."'
			ORDER BY `ac_access_level_name`
		";

		$columns['dhtmlxGrid']=array(
			'right_type_value'	=> array('grid' => true, 'window' => false, 'export' => true, 'col_type' => 'ro', 'width' => 250),
			'rolegroup_name'	=> array('grid' => true, 'window' => false, 'export' => true, 'col_type' => 'ro', 'width' => 250),
			'ac_access_level_name'	=> array('grid' => true, 'window' => false, 'export' => true, 'col_type' => 'ro', 'width' => 650),
			//'description'		=> array('grid' => true, 'window' => false, 'export' => true, 'col_type' => 'ro'),
			'right_type'		=> array(
											'grid'			=> false,
											'export'		=> true,
											'col_type'		=> 'combo',
											'options'		=>	array(
																	'mode'					=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
																	'array'	=> array(array("id"=>"EMPLOYEE","value"=>Dict::getValue("employee")),array("id"=>"VISITOR","value"=>Dict::getValue("visitor"))),
																),
											'label_text'	=> Dict::getValue("type"),
			),
			'rolegroup_id'		=> array(
											'grid'			=> false,
											'export'		=> true,
											'col_type'		=> 'combo',
											'options'		=>	array(
																	'mode'					=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
																	'modelSelectionModel'	=> $rolegroup,
																	'modelSelectionCriteria'=> $rolegroupCriteria,
																	'comboId'				=> 'rolegroup_id',
																	'comboValue'			=> 'rolegroup_name'
																),
											'label_text'	=> Dict::getValue("rolegroup"),
										),
			'ac_access_level_id'			=> array(
											'grid'			=> false,
											'export'		=> true,
											'col_type'		=> 'combo',
											'options'		=>	array(
																	'mode'			=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																	'sql'			=> $role_SQL,
																),
											'label_text'	=> Dict::getValue("profile_name"),
										),
		);

		return $columns;
	}

	public function attributeLabels()
	{
		$labels=array();
		
		$labels['dhtmlxGrid']=array(
			'right_type_value' => Dict::getValue("type"),
			'rolegroup_name' => Dict::getValue("rolegroup"),
			'ac_access_level_name' => Dict::getValue("profile_name"),
		);
		
		return $labels;
	}

	public static function getAvailableAccessLevelIDSQL($type = "EMPLOYEE" /*VISITOR*/, $column = "`ac_access_level_id`") {
		$rolegroup = Yang::getUserRoleGroup();

		if (App::getRight(null,"su")) {
			return "1";
		}

		$SQL = "
			SELECT
				aalr.`ac_access_level_id`
			FROM
				`ac_access_level_right` aalr
			LEFT JOIN
				`ac_access_level` aal ON
					aal.`ac_access_level_id` = aalr.`ac_access_level_id`
					AND aal.`status` = '".Status::PUBLISHED."'
			WHERE
				aalr.`status` = '".Status::PUBLISHED."'
				AND aalr.`right_type` = '$type'
				AND aalr.`rolegroup_id` = '$rolegroup'
				AND aal.`row_id` IS NOT NULL
		";

		$results = dbFetchAll($SQL);

		$access_level_ids = [];
		foreach ($results as $res) {
			$access_level_ids[] = $res["ac_access_level_id"];
		}

		if ((int)App::getSetting("use_ac_access_level_right")) {
			if (is_array($access_level_ids) && count($access_level_ids)) {
				return "$column IN ('".implode("','", $access_level_ids)."')";
			} else {
				return "0";
			}
		}

		return "1";
	}
}
?>
