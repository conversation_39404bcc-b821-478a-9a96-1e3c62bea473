<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Dict;
	use Yang;

`/yii2-only';


class AclController extends GridController
{
	public $layout = '//layouts/main';

	public function __construct()
	{
		parent::__construct("acl");
		$this->setModelName("AuthAcl");
		parent::setTitle(Dict::getValue("page_title_acl"));
//		parent::enableSubgrid(true);
//		parent::setRights(/*add*/true,/*mod*/true,/*imod*/false,/*del*/true,/*exp*/false,/*$sel*/false,/*$msel*/false,/*$details*/false);
		parent::exportSettings(Dict::getValue("export_file_user_management"));

		parent::selectConditions('`login_need` = 1');
	}

	public function columns()
	{

		$controllerArray = Dict::getDropdown('AuthController', 'controller_id', 'controller_dict_id');
		array_unshift($controllerArray, array('id'=> 'ALL','value'=> Dict::getValue('all')));

		$columnsSQL = " AND (t.`controller_id`='{AuthAcl_controller_id}')";

		$operationArraySQL = Dict::getDropdown('AuthOperation', 'operation_id', 'operation_dict_id', 'ttwa-base', "", true);
		$operationArraySQLCond = $operationArraySQL . " AND IF('{AuthAcl_column_name}'='',t.`type`='right',t.`type`='visibility')";

		$accessRightArray = App::getLookup('access_right');

		return array(
			'role_id'				=> array('export'=> true, 'col_type'=>'combo', 'options'=>array('comboModel'=>'AuthRole','comboId'=>'role_id','comboValue'=>'role_name')),
			'controller_id'			=> array('export'=> true, 'col_type'=>'combo', 'array'=>$controllerArray,'onChange'=>array('AuthAcl_operation_id','AuthAcl_column_name')),
			'column_name'			=> array('export'=> true, 'col_type'=>'combo', 'options'=>array('comboModel'=>'COL','selectConditions'=>$columnsSQL),'onChange'=>array('AuthAcl_operation_id')),
			'operation_id'			=> array('export'=> true, 'col_type'=>'combo', 'options'=>array('comboModel'=>'SQL','comboId'=>'id','comboValue'=>'value','selectConditions'=>$operationArraySQLCond,'selectConditionsGrid'=>$operationArraySQL),'onChange'=>array('AuthAcl_usergroup_id')),
//			'usergroup_id'			=> array('export'=> true, 'col_type'=>'combo', 'options'=>array('comboModel'=>'Usergroup','comboId'=>'usergroup_id','comboValue'=>'usergroup_name','selectConditions'=>"",'arrayMerge'=>array('user'=>Dict::getValue("user"),'all_users'=>Dict::getValue("all_users")),'visibility'=>"SELECT IF('{AuthAcl_operation_id}'='refine_query',1,0) as visible")),
			'access_right'			=> array('export'=> true, 'col_type'=>'combo', 'array'=>$accessRightArray),
		);
	}
}
?>
