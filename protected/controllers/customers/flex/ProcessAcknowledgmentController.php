<?php

Yang::loadComponentNamespaces('Absence');
Yang::loadComponentNamespaces('WorkScheduleCore');
Yang::loadComponentNamespaces('ProcessAcknowledgment');
Yang::loadComponentNamespaces('Employee');

use Components\Absence\Provider\StateTypeProvider;
use Components\Employee\Enum\EmployeeMainDataEnum;
use Components\ProcessAcknowledgment\Descriptor\ProcessAcknowledgmentDescriptor;
use Components\ProcessAcknowledgment\Enum\ProcessAcknowledgmentEnum;
use Components\ProcessAcknowledgment\Enum\ProcessAcknowledgmentSettingsEnum;
use Components\ProcessAcknowledgment\Helper\ProcessAcknowledgmentHelper;
use Components\ProcessAcknowledgment\Provider\EmployeeAbsenceChangesProvider;
use Components\ProcessAcknowledgment\Provider\EmployeeExtraHoursChangesProvider;
use Components\ProcessAcknowledgment\Provider\EmployeeWorkSchedulePublishedProvider;
use Components\ProcessAcknowledgment\Provider\ProcessAcknowledgmentDaytypeProvider;
use Components\ProcessAcknowledgment\Provider\ProcessAcknowledgmentProviderByECIDs;
use Components\ProcessAcknowledgment\Provider\WorkScheduleByUnitDaytypeProvider;
use Components\ProcessAcknowledgment\Provider\WorkScheduleOvertimeChangesProvider;
use Components\ProcessAcknowledgment\Transformer\AbsenceResultTransformer;
use Components\ProcessAcknowledgment\Transformer\OvertimeResultTransformer;
use Components\ProcessAcknowledgment\Transformer\StandbyResultTransformer;
use Components\ProcessAcknowledgment\Transformer\WorkScheduleResultTransformer;

final class ProcessAcknowledgmentController extends Grid2Controller
{
    private const EMPLOYEE_ABSENCE_ID = 'employee_absence_id';
    private const VALID_FROM = 'valid_from';
    private const VALID_TO = 'valid_to';
    private array $processAcknowledgmentSettings;
    private ProcessAcknowledgmentDescriptor $descriptor;
    private StateTypeProvider $stateTypeProvider;
    private AbsenceResultTransformer $absenceResultTransformer;
    private OvertimeResultTransformer $overtimeResultTransformer;
    private WorkScheduleResultTransformer $workScheduleResultTransformer;
    private StandbyResultTransformer $standbyResultTransformer;

    public function __construct()
    {
        parent::__construct('customers/flex/processAcknowledgment');

        $this->processAcknowledgmentSettings = json_decode(
            App::getSetting('feature_processAcknowledgment_settings'), true
        );
        $this->descriptor = new ProcessAcknowledgmentDescriptor();
        $this->stateTypeProvider = new StateTypeProvider();
        $this->absenceResultTransformer = new AbsenceResultTransformer();
        $this->overtimeResultTransformer = new OvertimeResultTransformer();
        $this->workScheduleResultTransformer = new WorkScheduleResultTransformer();
        $this->standbyResultTransformer = new StandbyResultTransformer();
    }

    protected function G2BInit(): void
    {
        parent::setControllerPageTitleId('page_title_process_acknowledgement');

        $path = Yang::addAsset(Yang::getAlias('application.assets.customers.flex'), false, -1, true);
        Yang::registerScriptFile($path . '/js/processAcknowledgment.js');

        $this->LAGridRights->overrideInitRights('search', false, 'dhtmlxGrid');
        $this->LAGridRights->overrideInitRights('reload', true, 'dhtmlxGrid');
        $this->LAGridRights->overrideInitRights('paging', true, 'dhtmlxGrid');
        $this->LAGridRights->overrideInitRights('select', true, 'dhtmlxGrid');
        $this->LAGridRights->overrideInitRights('multi_select', true, 'dhtmlxGrid');
        $this->LAGridRights->overrideInitRights('search_header', true, 'dhtmlxGrid');
        $this->LAGridRights->overrideInitRights('col_sorting', true, 'dhtmlxGrid');
        $this->LAGridRights->overrideInitRights('on_click', true, 'dhtmlxGrid');
        $this->LAGridRights->overrideInitRights('init_open_search', false, 'dhtmlxGrid');

        parent::enableLAGrid();
        $this->LAGridDB->enableArrMode();

        $this->LAGridDB->setPrimaryKey(ProcessAcknowledgmentEnum::REQUEST_IDENTIFIER);

        parent::setExportFileName(Dict::getValue('menu_item_process_acknowledgement'));
        parent::G2BInit();
    }

    protected function getStatusButtons($gridID = null): array
    {
        $buttons = parent::getStatusButtons($gridID);

        $buttons['select_all'] = [
            'type' => 'button',
            'id' => 'select_all',
            'class' => 'select_all',
            'name' => 'select_all',
            'img' => '/images/status_icons/st_multicheck.png',
            'label' => Dict::getModuleValue('ttwa-base', 'select_all'),
            'onclick' => 'laGrid_dhtmlxGrid.selectAll();',
        ];

        $buttons['approve'] = [
            'type' => 'button',
            'id' => 'approve',
            'class' => 'approve',
            'name' => 'approve',
            'img' => '/images/status_icons/st_accept.png',
            'label' => Dict::getModuleValue('ttwa-base', 'button_acknowledgment'),
            'onclick' => "approve('dhtmlxGrid', '" . Dict::getValue('are_you_sure_to_approve') . "', '" . Dict::getValue('please_select_line') . "')",
        ];
        $buttons['reject'] = [
            'type' => 'button',
            'id' => 'reject',
            'class' => 'reject',
            'name' => 'reject',
            'img' => '/images/status_icons/st_reject.png',
            'label' => Dict::getModuleValue('ttwa-base', 'button_reject'),
            'onclick' => "reject('dhtmlxGrid', '" . Dict::getValue('are_you_sure_to_reject') . ' - ' . Dict::getValue('warning_process_acknowledgement_reject_wsu') . "', '" . Dict::getValue('please_select_line') . "');",
        ];

        return $buttons;
    }

    public function columns(): array
    {
        return $this->columnRights([
            ProcessAcknowledgmentEnum::REQUEST_ID => ['export' => true, 'col_type' => 'ed', 'width' => '*'],
            ProcessAcknowledgmentEnum::AFFECTED_DAY => ['export' => true, 'col_type' => 'ed', 'width' => '*'],
            ProcessAcknowledgmentEnum::ORIGINAL_VALUE => ['export' => true, 'col_type' => 'ed', 'width' => '*'],
            ProcessAcknowledgmentEnum::NEW_VALUE => ['export' => true, 'col_type' => 'ed', 'width' => '*'],
            ProcessAcknowledgmentEnum::MODIFIED_BY => ['export' => true, 'col_type' => 'ed', 'width' => '*'],
            ProcessAcknowledgmentEnum::OPERATION_DATE => ['export' => true, 'col_type' => 'ed', 'width' => '*'],
        ]);
    }

    public function attributeLabels(): array
    {
        return [
            ProcessAcknowledgmentEnum::REQUEST_ID => Dict::getValue(ProcessAcknowledgmentEnum::OPERATION),
            ProcessAcknowledgmentEnum::AFFECTED_DAY => Dict::getValue(ProcessAcknowledgmentEnum::AFFECTED_DAY),
            ProcessAcknowledgmentEnum::ORIGINAL_VALUE => Dict::getValue(ProcessAcknowledgmentEnum::ORIGINAL_VALUE),
            ProcessAcknowledgmentEnum::NEW_VALUE => Dict::getValue(ProcessAcknowledgmentEnum::NEW_VALUE),
            ProcessAcknowledgmentEnum::MODIFIED_BY => Dict::getValue(ProcessAcknowledgmentEnum::MODIFIED_BY),
            ProcessAcknowledgmentEnum::OPERATION_DATE => Dict::getValue(ProcessAcknowledgmentEnum::OPERATION_DATE),
        ];
    }

    private function setSearchFilters(): array
    {
        return [
            self::VALID_FROM => date('Y-m-d'),
            self::VALID_TO => date(App::getSetting('defaultEnd')),
            'employee_contract' => Yang::getUserECID(),
        ];
    }

    /**
     * @throws Exception
     */
    protected function dataArray($gridID, $filter, $isExport = false): array
    {
        $absenceExceptionByCompanyIds =
            $this->processAcknowledgmentSettings[ProcessAcknowledgmentSettingsEnum::ABSENCE_EXCEPTIONS_BY_COMPANY_ID] ?? [];

        $requiredAbsencesByStateTypeIds =
            $this->processAcknowledgmentSettings[ProcessAcknowledgmentSettingsEnum::ABSENCE_ONLY_BY_STATE_TYPE_ID] ?? [];

        $searchFilter = $this->setSearchFilters();
        $employeeContractId = Yang::getUserECID();

        $this->descriptor->setSearchFilter($searchFilter);
        $this->descriptor->setValidFrom($searchFilter[self::VALID_FROM]);
        $this->descriptor->setValidTo($searchFilter[self::VALID_TO]);
        $this->descriptor->setProcessAcknowledgments(
            $this->getProcessAcknowledgmentByECIDs([$employeeContractId], $this->descriptor)
        );

        $this->descriptor->setDaytypes(ProcessAcknowledgmentHelper::modelsToArray(
            $this->getDayTypes($this->descriptor)
        ));

        $stateTypes = ($this->stateTypeProvider)();
        $employeeMainData = ProcessAcknowledgmentHelper::getEmployeesMainData($this->descriptor, [$employeeContractId]);

        // absence
        $employeeAbsences = $this->getEmployeeAbsences([$employeeContractId], $this->descriptor, $requiredAbsencesByStateTypeIds);
        $filteredEmployeeAbsences = ProcessAcknowledgmentHelper::filterEmployeeAbsencesByCompanyException($employeeAbsences, $employeeMainData, $employeeContractId, $absenceExceptionByCompanyIds);
        $employeeAbsencesUnhandled = ProcessAcknowledgmentHelper::filterUnhandledAbsences($filteredEmployeeAbsences, $this->descriptor);
        $groupedAbsences = ProcessAcknowledgmentHelper::groupByEmployeeAbsenceId($employeeAbsencesUnhandled);

        // work schedule overtime
        $workScheduleOvertimes = $this->getworkScheduleOvertimes([$employeeContractId], $this->descriptor);
        $wsoIndexedRowIds = ProcessAcknowledgmentHelper::indexByRowIds($workScheduleOvertimes);
        $wsoDaysToProcess = ProcessAcknowledgmentHelper::uniqueDayToProcess($workScheduleOvertimes, 'day');
        if (!empty($wsoDaysToProcess)) {
            $workScheduleForOvertimes = $this->getWorkScheduleForOvertimes(
                [$employeeContractId],
                min($wsoDaysToProcess),
                max($wsoDaysToProcess)
            );
        } else {
            $workScheduleForOvertimes = [];
        }

        // workSchedule
        $workSchedule = $this->getEmployeeWorkSchedule($this->descriptor, [$employeeContractId]);
        $unitIds = $this->getUnitIdsFromEmployeeMainData($employeeContractId, $this->descriptor, $employeeMainData);
        $workGroupIds = $this->getWorkroupdIdsFromEmployeeMainData($employeeContractId, $this->descriptor, $employeeMainData);

        $workScheduleByUnit = [];
        if (!empty($unitIds) && $workGroupIds) {
            $workScheduleByUnit = $this->getWorkSchedulesByUnit($this->descriptor, $unitIds, $workGroupIds);
        }

        // standby
        $standbys = $this->getStandby([$employeeContractId], $this->descriptor);
        $standbyIndexedRowIds = ProcessAcknowledgmentHelper::indexByRowIds($standbys);

        $results = array_merge(
            ...$this->absenceResultTransformer->transform($groupedAbsences, $stateTypes, true),
            ...$this->overtimeResultTransformer->transform(
                $this->descriptor,
                $workScheduleOvertimes,
                $wsoIndexedRowIds,
                $workScheduleForOvertimes,
                $employeeContractId,
                true
            ),
            ...$this->workScheduleResultTransformer->transform(
                $this->descriptor,
                $workScheduleByUnit,
                $workSchedule,
                $employeeMainData,
                $employeeContractId,
                true
            ),
            ...$this->standbyResultTransformer->transform(
                $this->descriptor,
                $standbys,
                $standbyIndexedRowIds,
                $employeeContractId,
                true
            )
        );

        if (!empty($results)) {
            array_multisort(
                array_column($results, EmployeeMainDataEnum::EMPLOYEE_CONTRACT_ID), SORT_ASC,
                array_column($results, ProcessAcknowledgmentEnum::AFFECTED_DAY), SORT_ASC,
                $results
            );
        }

        return array_filter($results);
    }

    private function getEmployeeAbsences(array $employeeContractIds, ProcessAcknowledgmentDescriptor $descriptor, array $stateTypeIds): array
    {
        return ProcessAcknowledgmentHelper::modelsToArray((new EmployeeAbsenceChangesProvider())($employeeContractIds, $descriptor, $stateTypeIds));
    }

    private function getEmployeeAbsencesByEmployeeAbsenceId(array $employeeAbsenceIds, string $validFrom, string $validTo): array
    {
        $criteria = new CDbCriteria();
        $criteria->condition =
            "employee_absence_id IN ('" . implode("','", $employeeAbsenceIds) . "') " .
            "AND day BETWEEN '" . $validFrom . "' AND '" . $validTo . "' " .
            "AND status IN ('" . Status::PUBLISHED . "', '" . Status::DELETED . "')";

        return ProcessAcknowledgmentHelper::modelsToArray(EmployeeAbsence::model()->findAll($criteria));
    }

    private function getWorkScheduleOvertimesByRowId(array $identifiers): array
    {
        $criteria = new CDbCriteria();
        $criteria->condition =
            "row_id IN ('" . implode("','", $identifiers) . "') " .
            "AND status IN ('" . Status::PUBLISHED . "', '" . Status::DELETED . "')";

        return ProcessAcknowledgmentHelper::modelsToArray(WorkScheduleOvertime::model()->findAll($criteria));
    }


    private function getStandbysByRowId(array $identifiers): array
    {
        $criteria = new CDbCriteria();
        $criteria->condition =
            "row_id IN ('" . implode("','", $identifiers) . "') " .
            "AND status IN ('" . Status::PUBLISHED . "', '" . Status::DELETED . "')";

        return ProcessAcknowledgmentHelper::modelsToArray(EmployeeExtraHours::model()->findAll($criteria));
    }

    private function getWorkSchedulesByRowId(array $identifiers): array
    {
        $criteria = new CDbCriteria();
        $criteria->condition =
            "row_id IN ('" . implode("','", $identifiers) . "') " .
            "AND status IN ('" . Status::PUBLISHED . "', '" . Status::DELETED . "')";

        return ProcessAcknowledgmentHelper::modelsToArray(WorkScheduleUsed::model()->findAll($criteria));
    }

    private function getworkScheduleOvertimes(array $employeeContractId, ProcessAcknowledgmentDescriptor $descriptor): array
    {
        return ProcessAcknowledgmentHelper::modelsToArray((new WorkScheduleOvertimeChangesProvider())($employeeContractId, $descriptor));
    }

    private function getProcessAcknowledgmentByECIDs(array $employeeContractIds, ProcessAcknowledgmentDescriptor $descriptor): array
    {
        return ProcessAcknowledgmentHelper::modelsToArray((new ProcessAcknowledgmentProviderByECIDs())($employeeContractIds, $descriptor));
    }

    public function actionApprove(): void
    {
        $this->processActionSave(ProcessAcknowledgmentEnum::APPROVED);
    }

    public function actionReject(): void
    {
        $this->processActionSave(ProcessAcknowledgmentEnum::REJECTED);
    }

    private function processActionSave(string $acknowledgementStatus): void
    {
        $this->layout = '//layouts/ajax';

        $status = [
            'status' => 1,
            'msg' => ['title' => Dict::getValue('process_ackwnodlegment_save_status'), 'body' => Dict::getValue('successful_save')]
        ];

        $ids = explode(',', requestParam('selected_ids'));
        $searchFilter = $this->setSearchFilters();
        $validFrom = $searchFilter[self::VALID_FROM];
        $validTo = $searchFilter[self::VALID_TO];

        $transaction = Yang::app()->db->beginTransaction();
        try {
            $formattedIds = $this->formatIds($ids); // all ids
            // absence
            $employeeAbsenceIds = $this->indexByProcessId($formattedIds, ProcessAcknowledgmentEnum::IDENTIFIER_ABSENCE);
            $employeeAbsences = $this->fetchEmployeeAbsences($employeeAbsenceIds, $validFrom, $validTo);
            // workScheduleOvertime
            $workScheduleOvertimesIds = $this->indexByProcessId($formattedIds, ProcessAcknowledgmentEnum::IDENTIFIER_WSO_OVERTIME);
            $workScheduleOvertimes = $this->fetchWorkScheduleOvertimes($workScheduleOvertimesIds);
            // standbys
            $standbyIds = $this->indexByProcessId($formattedIds, ProcessAcknowledgmentEnum::IDENTIFIER_EXTRA_HOURS_STANDBY);
            $standbys = $this->fetchStandbys($standbyIds);
            // workSchedule
            $workScheduleIds = $this->indexByProcessId($formattedIds, ProcessAcknowledgmentEnum::IDENTIFIER_WORKSCHEDULE);
            $workSchedules = $this->fetchWorkSchedules($workScheduleIds);

            if ($acknowledgementStatus !== ProcessAcknowledgmentEnum::REJECTED) {
                $this->processAbsencesToProcessAcknowledgments($formattedIds, $employeeAbsences, $acknowledgementStatus);
                $this->processStandbysToProcessAcknowledgments($formattedIds, $standbys, $acknowledgementStatus);
                $this->processWorkSchedulesToProcessAcknowledgments($formattedIds, $workSchedules, $acknowledgementStatus);
            }

            $this->processOvertimesToProcessAcknowledgments($formattedIds, $workScheduleOvertimes, $acknowledgementStatus);

            $transaction->commit();
            echo json_encode($status);
        } catch (Exception $ex) {
            $transaction->rollback();
        }
    }

    private function formatIds(array $ids): array
    {
        $uniqueIds = array_unique(array_filter($ids));
        return array_map(function ($id) {
            $parts = explode('_', $id, 2);
            if (count($parts) === 2) {
                [$prefix, $identifier] = $parts;
                return [$prefix => $identifier];
            }
            return [];
        }, $uniqueIds);
    }

    private function indexByProcessId(array $formattedIds, string $processId): array
    {
        return array_values(array_map(function ($item) use ($processId) {
            return $item[$processId];
        }, array_filter($formattedIds, function ($item) use ($processId) {
            return isset($item[$processId]);
        })));
    }

    private function fetchEmployeeAbsences(array $employeeAbsenceIds, string $validFrom, string $validTo): array
    {
        if (empty($employeeAbsenceIds)) {
            return [];
        }

        $absences = $this->getEmployeeAbsencesByEmployeeAbsenceId($employeeAbsenceIds, $validFrom, $validTo);
        return array_reduce($absences, function ($carry, $absence) {
            $carry[$absence[self::EMPLOYEE_ABSENCE_ID]][] = $absence;
            return $carry;
        }, []);
    }

    private function fetchWorkScheduleOvertimes(array $identifiers): array
    {
        if (empty($identifiers)) {
            return [];
        }

        $workScheduleOvertimes = $this->getWorkScheduleOvertimesByRowId($identifiers);

        return array_reduce($workScheduleOvertimes, function ($carry, $overtime) {
            $carry[$overtime['row_id']][] = $overtime;
            return $carry;
        }, []);
    }

    private function fetchStandbys(array $standbyIds): array
    {
        if (empty($standbyIds)) {
            return [];
        }

        $standbys = $this->getStandbysByRowId($standbyIds);
        return array_reduce($standbys, function ($carry, $standby) {
            $carry[$standby['row_id']][] = $standby;
            return $carry;
        }, []);
    }

    private function fetchWorkSchedules(array $workSchedules): array
    {
        if (empty($workSchedules)) {
            return [];
        }

        $workSchedules = $this->getWorkSchedulesByRowId($workSchedules);
        return array_reduce($workSchedules, function ($carry, $workSchedule) {
            $carry[$workSchedule['row_id']][] = $workSchedule;
            return $carry;
        }, []);
    }

    private function processAbsencesToProcessAcknowledgments(array $formattedIds, array $employeeAbsences, string $acknowledgementStatus): void
    {
        $formattedIds = array_values(array_filter($formattedIds, function ($item) {
            return isset($item[ProcessAcknowledgmentEnum::IDENTIFIER_ABSENCE]);
        }));

        foreach ($formattedIds as $item) {
            $identifier = $item[ProcessAcknowledgmentEnum::IDENTIFIER_ABSENCE] ?? null;
            if ($identifier && isset($employeeAbsences[$identifier])) {
                foreach ($employeeAbsences[$identifier] as $absence) {
                    $processAcknowledgment = new ProcessAcknowledgment();
                    $processAcknowledgment->day = $absence['day'];
                    $processAcknowledgment->employee_contract_id = $absence[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_ID];
                    $processAcknowledgment->process_id = ProcessAcknowledgmentEnum::IDENTIFIER_ABSENCE;
                    $processAcknowledgment->process_identifier = $absence['row_id'];
                    $processAcknowledgment->acknowledgement_status = $acknowledgementStatus;
                    $processAcknowledgment->status = Status::PUBLISHED;
                    $processAcknowledgment->created_by = userID();
                    $processAcknowledgment->created_on = date('Y-m-d H:i:s');
                    $processAcknowledgment->save();
                }
            }
        }
    }

    private function processOvertimesToProcessAcknowledgments(array $formattedIds, array $workScheduleOvertimes, string $acknowledgementStatus): void
    {
        $formattedIds = array_values(array_filter($formattedIds, function ($item) {
            return isset($item[ProcessAcknowledgmentEnum::IDENTIFIER_WSO_OVERTIME]);
        }));

        foreach ($formattedIds as $item) {
            $identifier = $item[ProcessAcknowledgmentEnum::IDENTIFIER_WSO_OVERTIME] ?? null;
            if ($identifier && isset($workScheduleOvertimes[$identifier])) {
                foreach ($workScheduleOvertimes[$identifier] as $overtime) {
                    $processAcknowledgment = new ProcessAcknowledgment();
                    $processAcknowledgment->day = $overtime['day'];
                    $processAcknowledgment->employee_contract_id = $overtime[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_ID];
                    $processAcknowledgment->process_id = ProcessAcknowledgmentEnum::IDENTIFIER_WSO_OVERTIME;
                    $processAcknowledgment->process_identifier = $overtime['row_id'];
                    $processAcknowledgment->acknowledgement_status = $acknowledgementStatus;
                    $processAcknowledgment->status = Status::PUBLISHED;
                    $processAcknowledgment->created_by = userID();
                    $processAcknowledgment->created_on = date('Y-m-d H:i:s');
                    $processAcknowledgment->save();
                }
            }
        }
    }

    private function processStandbysToProcessAcknowledgments(array $formattedIds, array $standbys, string $acknowledgementStatus): void
    {
        $formattedIds = array_values(array_filter($formattedIds, function ($item) {
            return isset($item[ProcessAcknowledgmentEnum::IDENTIFIER_EXTRA_HOURS_STANDBY]);
        }));

        foreach ($formattedIds as $item) {
            $identifier = $item[ProcessAcknowledgmentEnum::IDENTIFIER_EXTRA_HOURS_STANDBY] ?? null;
            if ($identifier && isset($standbys[$identifier])) {
                foreach ($standbys[$identifier] as $standby) {
                    $processAcknowledgment = new ProcessAcknowledgment();
                    $processAcknowledgment->day = $standby['day'];
                    $processAcknowledgment->employee_contract_id = $standby[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_ID];
                    $processAcknowledgment->process_id = ProcessAcknowledgmentEnum::IDENTIFIER_EXTRA_HOURS_STANDBY;
                    $processAcknowledgment->process_identifier = $standby['row_id'];
                    $processAcknowledgment->acknowledgement_status = $acknowledgementStatus;
                    $processAcknowledgment->status = Status::PUBLISHED;
                    $processAcknowledgment->created_by = userID();
                    $processAcknowledgment->created_on = date('Y-m-d H:i:s');
                    $processAcknowledgment->save();
                }
            }
        }
    }

    private function processWorkSchedulesToProcessAcknowledgments(array $formattedIds, array $workSchedules, string $acknowledgementStatus): void
    {
        $formattedIds = array_values(array_filter($formattedIds, function ($item) {
            return isset($item[ProcessAcknowledgmentEnum::IDENTIFIER_WORKSCHEDULE]);
        }));

        foreach ($formattedIds as $item) {
            $identifier = $item[ProcessAcknowledgmentEnum::IDENTIFIER_WORKSCHEDULE] ?? null;
            if ($identifier && isset($workSchedules[$identifier])) {
                foreach ($workSchedules[$identifier] as $workSchedule) {
                    $processAcknowledgment = new ProcessAcknowledgment();
                    $processAcknowledgment->day = $workSchedule['day'];
                    $processAcknowledgment->employee_contract_id = $workSchedule[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_ID];
                    $processAcknowledgment->process_id = ProcessAcknowledgmentEnum::IDENTIFIER_WORKSCHEDULE;
                    $processAcknowledgment->process_identifier = $workSchedule['row_id'];
                    $processAcknowledgment->acknowledgement_status = $acknowledgementStatus;
                    $processAcknowledgment->status = Status::PUBLISHED;
                    $processAcknowledgment->created_by = userID();
                    $processAcknowledgment->created_on = date('Y-m-d H:i:s');
                    $processAcknowledgment->save();
                }
            }
        }
    }

    private function getWorkScheduleForOvertimes(array $employeeContractIds, string $validFrom, string $validTo)
    {
        $gews = new GetEmployeeWorkSchedule($validFrom, $validTo, $employeeContractIds, true);

        return $gews->get();
    }

    /**
     * @throws DateMalformedStringException
     * @throws DateMalformedPeriodStringException
     */
    private function getUnitIdsFromEmployeeMainData(string $employeeContractId, ProcessAcknowledgmentDescriptor $descriptor, EmployeesWithMainData $employeeMainData): array
    {
        $dateRange = new DatePeriod(
            new DateTime($descriptor->getValidFrom()),
            new DateInterval('P1D'),
            (clone new DateTime($descriptor->getValidTo()))->modify('+1 day')
        );

        $unitIds = array_map(
            fn($date) => $employeeMainData->get($employeeContractId, $date->format('Y-m-d'))[EmployeeMainDataEnum::UNIT_ID],
            iterator_to_array($dateRange)
        );

        return array_unique($unitIds);
    }

    /**
     * @throws DateMalformedStringException
     * @throws DateMalformedPeriodStringException
     */
    private function getWorkroupdIdsFromEmployeeMainData(string $employeeContractId, ProcessAcknowledgmentDescriptor $descriptor, EmployeesWithMainData $employeeMainData): array
    {
        $dateRange = new DatePeriod(
            new DateTime($descriptor->getValidFrom()),
            new DateInterval('P1D'),
            (clone new DateTime($descriptor->getValidTo()))->modify('+1 day')
        );

        $workgroupIds = array_map(
            fn($date) => $employeeMainData->get($employeeContractId, $date->format('Y-m-d'))[EmployeeMainDataEnum::WORKGROUP_ID],
            iterator_to_array($dateRange)
        );

        return array_unique($workgroupIds);
    }

    private function getWorkSchedulesByUnit(ProcessAcknowledgmentDescriptor $descriptor, array $unitIds, array $workGroupIds): array
    {
        return (new WorkScheduleByUnitDaytypeProvider())($descriptor, $unitIds, $workGroupIds);
    }

    private function getEmployeeWorkSchedule(ProcessAcknowledgmentDescriptor $descriptor, array $employeeContractIds): array
    {
        return ProcessAcknowledgmentHelper::modelsToArray((new EmployeeWorkSchedulePublishedProvider())($descriptor, $employeeContractIds));
    }

    private function getDayTypes(ProcessAcknowledgmentDescriptor $descriptor): array
    {
        return (new ProcessAcknowledgmentDaytypeProvider())($descriptor);
    }

    private function getStandby(array $employeeContractIds, ProcessAcknowledgmentDescriptor $descriptor): array
    {
        return ProcessAcknowledgmentHelper::modelsToArray((new EmployeeExtraHoursChangesProvider())($employeeContractIds, $descriptor));
    }

}
